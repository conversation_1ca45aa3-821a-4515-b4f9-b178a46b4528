import serial
import cv2
import numpy as np
import time
import threading
import logging
import signal
import sys
from ultralytics import YOLO

# 全局配置
SHOW_VIDEO = False  # 生产环境关闭视频显示
LOG_LEVEL = logging.INFO  # 日志级别
LOG_FILE = "/home/<USER>/yolo11/medicine_car.log"  # 日志文件路径

# 通信协议参数
HEADER = bytes([0xAA, 0x55])
CMD_TARGET = 0x10  # 目标位置信息

# 配置日志
logging.basicConfig(
    level=LOG_LEVEL,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UARTCommunicator:
    def __init__(self):
        try:
            self.ser = serial.Serial(
                port='/dev/ttyTHS0',
                baudrate=115200,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            logger.info("串口初始化成功")
        except serial.SerialException as e:
            logger.error(f"串口初始化失败: {str(e)}")
            raise
        
        self.running = True
        self.thread = threading.Thread(target=self._receive_thread)
        self.thread.daemon = True
        self.thread.start()
        logger.info("串口接收线程已启动")
    
    def _receive_thread(self):
        """串口接收线程"""
        buffer = bytearray()
        while self.running:
            try:
                if self.ser.in_waiting > 0:
                    data = self.ser.read(self.ser.in_waiting)
                    buffer.extend(data)
                    
                    # 处理完整帧
                    while len(buffer) >= 6:  # 最小帧长度
                        # 查找帧头
                        idx = buffer.find(HEADER)
                        if idx == -1:
                            buffer.clear()
                            break
                        
                        # 移除帧头前的无效数据
                        if idx > 0:
                            buffer = buffer[idx:]
                        
                        # 检查帧完整性
                        if len(buffer) < 5:  # 帧头+CMD+LEN
                            break
                        
                        cmd = buffer[2]
                        data_len = buffer[3]
                        total_len = 5 + data_len  # 帧头(2)+CMD(1)+LEN(1)+DATA(LEN)+SUM(1)
                        
                        if len(buffer) < total_len:
                            break  # 等待更多数据
                        
                        # 提取完整帧
                        frame = buffer[:total_len]
                        buffer = buffer[total_len:]
                        
                        # 校验检查
                        checksum = sum(frame[2:-1]) & 0xFF
                        if checksum == frame[-1]:
                            self.handle_received_frame(frame)
                        else:
                            logger.warning(f"校验和错误: 计算值={checksum}, 接收值={frame[-1]}")
            except Exception as e:
                logger.error(f"串口接收线程错误: {str(e)}")
                time.sleep(0.1)
    
    def send_room_numbers(self, numbers):
        """发送病房号及位置信息给下位机"""
        try:
            if not numbers:
                return False
                
            # 构建数据包: [数字1, 位置1, 数字2, 位置2, ...]
            data = bytearray()
            
            if len(numbers) == 1:
                # 单个数字: 位置为1
                data.append(numbers[0])
                data.append(1)
            elif len(numbers) == 2:
                # 两个数字: 左侧位置0, 右侧位置2
                data.append(numbers[0])
                data.append(0)
                data.append(numbers[1])
                data.append(2)
            elif len(numbers) == 4:
                # 四个数字: 左侧两个位置0, 右侧两个位置2
                for i, num in enumerate(numbers):
                    position = 0 if i < 2 else 2
                    data.append(num)
                    data.append(position)
            else:
                logger.warning(f"无效的数字数量: {len(numbers)}")
                return False
                
            self.send_packet(CMD_TARGET, data)
            logger.info(f"发送数据: 数字={numbers}, 位置数据={list(data)}")
            return True
        except Exception as e:
            logger.error(f"发送病房号错误: {str(e)}")
            return False
    
    def send_packet(self, cmd, data=bytes()):
        """发送数据包"""
        try:
            data_len = len(data)
            packet = HEADER + bytes([cmd, data_len]) + data
            checksum = sum(packet[2:]) & 0xFF
            packet += bytes([checksum])
            self.ser.write(packet)
        except Exception as e:
            logger.error(f"发送数据包错误: {str(e)}")
    
    def handle_received_frame(self, frame):
        """处理接收到的数据帧"""
        if len(frame) < 5:
            return
        
        cmd = frame[2]
        data_len = frame[3]
        data = frame[4:4+data_len]
        
        # 处理下位机响应
        logger.info(f"收到下位机响应: 指令={cmd}, 数据={data.hex()}")
    
    def close(self):
        """关闭串口资源"""
        logger.info("关闭串口...")
        self.running = False
        if hasattr(self, 'thread') and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        if hasattr(self, 'ser') and self.ser.is_open:
            self.ser.close()
        logger.info("串口资源已释放")

class RoomDetector:
    def __init__(self, camera_id="/dev/video0"):
        try:
            self.cap = cv2.VideoCapture(camera_id)
            if not self.cap.isOpened():
                raise RuntimeError("无法打开摄像头")
            
            # 设置相机参数
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            logger.info("摄像头初始化成功")
        except Exception as e:
            logger.error(f"摄像头初始化失败: {str(e)}")
            raise
        
        # 状态管理
        self.last_rooms = []  # 改为存储多个数字
        self.last_sent_rooms = []  # 改为存储多个数字
        self.detection_count = 0
        self.detection_threshold = 5  # 连续检测到8次相同结果才认为有效
        self.cooldown = 0  # 发送后的冷却时间
        
        # 性能监控
        self.frame_count = 0
        self.start_time = time.time()
        self.last_fps_time = self.start_time
        self.fps = 0

        try:
            # 加载YOLO模型
            self.model = YOLO("best.pt")
            logger.info(f"模型加载成功, 类别: {self.model.names}")
            self.conf_threshold = 0.75  # 置信度阈值
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise
    
    def detect_room_numbers(self, frame):
        """检测病房号并返回排序后的数字列表(从左到右)"""
        try:
            # YOLO推理
            results = self.model(
                frame,
                device="cuda",
                conf=self.conf_threshold,
                verbose=False
            )
            
            detected_numbers = []
            
            # 处理所有检测结果
            if results[0].boxes.xyxy.numel() > 0:
                # 获取所有检测结果
                boxes = results[0].boxes.xyxy.cpu().numpy()
                confidences = results[0].boxes.conf.cpu().numpy()
                class_ids = results[0].boxes.cls.cpu().numpy().astype(int)
                
                # 根据x坐标排序 (从左到右)
                sorted_indices = np.argsort([box[0] for box in boxes])
                
                for idx in sorted_indices:
                    class_id = class_ids[idx]
                    confidence = confidences[idx]
                    
                    # 确保类别名称是数字
                    try:
                        room_num = int(self.model.names[class_id])
                        detected_numbers.append(room_num)
                        logger.debug(f"检测到: {room_num} (置信度: {confidence:.2f})")
                    except ValueError:
                        logger.warning(f"无效的类别名称: {self.model.names[class_id]}")
            
            # 只保留有效数量的数字 (1, 2或4)
            if len(detected_numbers) in (1, 2, 4):
                return detected_numbers
            else:
                if detected_numbers:
                    logger.debug(f"检测到 {len(detected_numbers)} 个数字，但只支持1/2/4个数字，已忽略")
                return []
        except Exception as e:
            logger.error(f"病房号检测错误: {str(e)}")
            return []
    
    def process_frame(self):
        """处理一帧图像并返回检测到的数字列表"""
        try:
            ret, frame = self.cap.read()
            if not ret:
                logger.warning("无法读取摄像头帧")
                return [], None
            
            # 检测病房号
            room_numbers = self.detect_room_numbers(frame)
            
            # 更新性能计数
            self.frame_count += 1
            current_time = time.time()
            if current_time - self.last_fps_time >= 1.0:  # 每秒更新一次FPS
                self.fps = self.frame_count / (current_time - self.last_fps_time)
                self.frame_count = 0
                self.last_fps_time = current_time
                logger.debug(f"当前FPS: {self.fps:.1f}")
            
            # 准备显示帧
            display_frame = None
            if SHOW_VIDEO:
                display_frame = frame.copy()
                detection_text = f"检测: {room_numbers if room_numbers else '无'}"
                cv2.putText(display_frame, detection_text,
                            (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                sent_text = f"发送: {self.last_sent_rooms if self.last_sent_rooms else '无'}"
                cv2.putText(display_frame, sent_text,
                            (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                cv2.putText(display_frame, f"FPS: {self.fps:.1f}", 
                            (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            return room_numbers, display_frame
        except Exception as e:
            logger.error(f"帧处理错误: {str(e)}")
            return [], None
    
    def update_state(self, current_rooms):
        """更新检测状态并决定是否发送"""
        # 冷却期处理
        if self.cooldown > 0:
            self.cooldown -= 1
            return False
        
        # 检测到有效房间号集合
        if current_rooms:
            # 与上一次检测相同
            if current_rooms == self.last_rooms:
                self.detection_count += 1
                
                # 达到阈值且不同于上次发送的房间号集合
                if (self.detection_count >= self.detection_threshold and 
                    current_rooms != self.last_sent_rooms):
                    self.last_sent_rooms = current_rooms.copy()
                    self.cooldown = 30  # 30帧冷却时间（约1秒）
                    self.detection_count = 0
                    logger.info(f"准备发送病房号: {current_rooms}")
                    return True
            else:
                # 检测到新的房间号集合，重置计数
                self.last_rooms = current_rooms.copy()
                self.detection_count = 1
        else:
            # 未检测到房间号，重置状态
            self.last_rooms = []
            self.detection_count = 0
        
        return False
    
    def release(self):
        """释放资源"""
        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()
        logger.info("摄像头资源已释放")

def main():
    logger.info("启动病房号检测系统...")
    
    # 初始化通信和检测模块
    uart = None
    detector = None
    
    try:
        uart = UARTCommunicator()
        detector = RoomDetector()
        
        # 如果需要显示视频流，创建窗口
        if SHOW_VIDEO:
            cv2.namedWindow('病房号检测系统', cv2.WINDOW_NORMAL)
            cv2.resizeWindow('病房号检测系统', 640, 480)
        
        # 主循环
        while True:
            start_time = time.time()
            
            # 处理一帧图像
            room_numbers, frame = detector.process_frame()
            
            # 更新状态并决定是否发送
            if room_numbers and detector.update_state(room_numbers):
                uart.send_room_numbers(room_numbers)
            
            # 显示处理结果
            if SHOW_VIDEO and frame is not None:
                cv2.imshow('病房号检测系统', frame)
            
            # 退出检测
            key = cv2.waitKey(1) & 0xFF if SHOW_VIDEO else 0
            if key == ord('q'):
                logger.info("用户请求退出")
                break
            
            # 控制处理频率 (约15FPS)
            elapsed = time.time() - start_time
            if elapsed < 0.066:
                time.sleep(0.066 - elapsed)
    
    except KeyboardInterrupt:
        logger.info("程序被用户终止")
    except Exception as e:
        logger.error(f"主循环错误: {str(e)}", exc_info=True)
    finally:
        # 清理资源
        if detector:
            detector.release()
        if uart:
            uart.close()
        if SHOW_VIDEO:
            cv2.destroyAllWindows()
        logger.info("系统已关闭")

# 信号处理函数
def signal_handler(sig, frame):
    logger.info("收到终止信号，准备退出...")
    # 这里不需要手动清理，因为 finally 块会处理
    sys.exit(0)

if __name__ == "__main__":
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    main()