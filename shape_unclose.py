import cv2
import numpy as np
import math

# ROI矩形检测参数
MIN_RECT_AREA = 1000      # 最小矩形面积
MAX_RECT_AREA = 50000     # 最大矩形面积
RECT_ASPECT_RATIO_MIN = 0.3  # 矩形最小宽高比
RECT_ASPECT_RATIO_MAX = 3.0  # 矩形最大宽高比

# 非闭合轮廓检测参数
MIN_OPEN_CONTOUR_LENGTH = 30    # 最小开放轮廓长度
CLOSURE_THRESHOLD = 15          # 闭合判断阈值（起点终点距离）

# 工作流程状态
workflow_state = "detecting_roi"  # detecting_roi, analyzing_open_contours
selected_roi = None

def detect_rectangles(edges):
    """检测矩形ROI区域"""
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    rectangles = []

    for contour in contours:
        area = cv2.contourArea(contour)
        if area < MIN_RECT_AREA or area > MAX_RECT_AREA:
            continue

        # 多边形逼近
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # 检查是否为四边形（矩形）
        if len(approx) == 4:
            x, y, w, h = cv2.boundingRect(approx)
            aspect_ratio = float(w) / h
            if RECT_ASPECT_RATIO_MIN <= aspect_ratio <= RECT_ASPECT_RATIO_MAX:
                rectangles.append({
                    'contour': contour,
                    'bbox': (x, y, w, h),
                    'area': area,
                    'aspect_ratio': aspect_ratio
                })

    # 按面积排序，返回最大的矩形作为主ROI
    rectangles.sort(key=lambda x: x['area'], reverse=True)
    return rectangles

def is_open_contour(contour):
    """判断轮廓是否为非闭合（开放）轮廓"""
    if len(contour) < 10:
        return False, 0, 0  # 返回三个值保持一致

    # 计算起点和终点的距离
    start_point = contour[0][0]
    end_point = contour[-1][0]
    distance = np.sqrt((end_point[0] - start_point[0])**2 +
                      (end_point[1] - start_point[1])**2)

    # 计算轮廓周长
    perimeter = cv2.arcLength(contour, False)  # False表示开放轮廓

    # 判断是否为开放图形
    is_open = (distance > CLOSURE_THRESHOLD and
              perimeter > MIN_OPEN_CONTOUR_LENGTH and
              distance / perimeter > 0.1)  # 距离占周长比例

    return is_open, distance, perimeter

def analyze_open_contours_in_roi(img, roi_bbox):
    """分析ROI区域内的非闭合轮廓"""
    x, y, w, h = roi_bbox
    roi_img = img[y:y+h, x:x+w]

    # 预处理ROI区域
    gray_roi = cv2.cvtColor(roi_img, cv2.COLOR_BGR2GRAY)
    blurred_roi = cv2.GaussianBlur(gray_roi, (3, 3), 0)
    edges_roi = cv2.Canny(blurred_roi, 50, 150)

    # 查找ROI内的轮廓
    contours, _ = cv2.findContours(edges_roi, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

    open_contours = []
    for contour in contours:
        is_open, distance, perimeter = is_open_contour(contour)

        if is_open:
            # 转换为全局坐标
            contour_global = contour + np.array([x, y])

            # 计算轮廓特征
            start_point = tuple(contour[0][0] + np.array([x, y]))
            end_point = tuple(contour[-1][0] + np.array([x, y]))

            # 计算轮廓中心
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx_local = int(M["m10"] / M["m00"])
                cy_local = int(M["m01"] / M["m00"])
                center_global = (cx_local + x, cy_local + y)
            else:
                center_global = start_point

            open_contours.append({
                'contour': contour_global,
                'start_point': start_point,
                'end_point': end_point,
                'center': center_global,
                'perimeter': perimeter,
                'openness': distance / perimeter,
                'start_end_distance': distance
            })

    return open_contours

# 初始化摄像头
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

print("=== 矩形ROI + 非闭合轮廓检测 ===")
print("工作流程:")
print("1. 自动检测矩形ROI区域")
print("2. 在ROI内检测非闭合轮廓")
print("按 'q' 键退出程序")
print("=" * 40)

frame_count = 0

while True:
    frame_count += 1

    # 读取图像
    ret, img = cap.read()
    if not ret:
        print("无法读取摄像头画面")
        break

    # 创建显示用的图像副本
    img_display = img.copy()

    # 图像预处理
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 50, 150)

    # === 工作流程处理 ===
    if workflow_state == "detecting_roi":
        # 步骤1: 检测矩形ROI
        rectangles = detect_rectangles(edges)

        # 绘制检测到的矩形
        for i, rect in enumerate(rectangles):
            contour = rect['contour']
            bbox = rect['bbox']
            x, y, w, h = bbox

            cv2.drawContours(img_display, [contour], -1, (0, 255, 0), 2)
            cv2.rectangle(img_display, (x, y), (x+w, y+h), (255, 255, 0), 1)
            cv2.putText(img_display, f"ROI {i}", (x, y-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(img_display, f"Area: {rect['area']:.0f}", (x, y+h+20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

        # 自动选择最大的矩形作为ROI
        if len(rectangles) > 0:
            selected_roi = rectangles[0]
            workflow_state = "analyzing_open_contours"
            print(f"自动选择ROI: {selected_roi['bbox']}, 面积: {selected_roi['area']:.0f}")

        # 状态提示
        cv2.putText(img_display, "Step 1: Detecting Rectangle ROI", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(img_display, f"Found {len(rectangles)} rectangles", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    elif workflow_state == "analyzing_open_contours":
        # 步骤2: 在ROI内检测非闭合轮廓
        if selected_roi:
            # 绘制选中的ROI
            bbox = selected_roi['bbox']
            x, y, w, h = bbox
            cv2.rectangle(img_display, (x, y), (x+w, y+h), (0, 255, 0), 2)
            cv2.putText(img_display, "Selected ROI", (x, y-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

            # 分析ROI内的非闭合轮廓
            open_contours = analyze_open_contours_in_roi(img, bbox)

            # 绘制非闭合轮廓
            for i, contour_info in enumerate(open_contours):
                contour = contour_info['contour']
                start_point = contour_info['start_point']
                end_point = contour_info['end_point']
                center = contour_info['center']

                # 绘制轮廓
                cv2.drawContours(img_display, [contour], -1, (255, 0, 0), 2)

                # 标注起点和终点
                cv2.circle(img_display, start_point, 6, (0, 255, 0), -1)  # 绿色起点
                cv2.circle(img_display, end_point, 6, (0, 0, 255), -1)    # 红色终点

                # 连接起点和终点（显示开放程度）
                cv2.line(img_display, start_point, end_point, (255, 255, 255), 1)

                # 标注信息
                cv2.putText(img_display, f"Open-{i}", center,
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
                cv2.putText(img_display, f"L:{contour_info['perimeter']:.0f}",
                           (center[0], center[1]+20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
                cv2.putText(img_display, f"O:{contour_info['openness']:.2f}",
                           (center[0], center[1]+35),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)

            # 状态提示
            cv2.putText(img_display, "Step 2: Analyzing Open Contours in ROI", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(img_display, f"Found {len(open_contours)} open contours", (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 打印检测结果
            if len(open_contours) > 0:
                print(f"Frame {frame_count}: 检测到 {len(open_contours)} 个非闭合轮廓")
                for i, contour_info in enumerate(open_contours):
                    print(f"  轮廓 {i}: 周长={contour_info['perimeter']:.1f}, "
                          f"开放度={contour_info['openness']:.3f}, "
                          f"起点={contour_info['start_point']}, "
                          f"终点={contour_info['end_point']}")

        # 如果ROI丢失，重新检测
        rectangles = detect_rectangles(edges)
        if len(rectangles) == 0:
            workflow_state = "detecting_roi"
            selected_roi = None
            print("ROI丢失，重新检测矩形")

    # 显示图像
    cv2.imshow('ROI + Open Contour Detection', img_display)

    # 检查按键退出
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break

# 释放资源
cap.release()
cv2.destroyAllWindows()
