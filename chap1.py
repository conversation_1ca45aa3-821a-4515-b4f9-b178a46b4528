import cv2
import numpy as np
def main():
    # 初始化摄像头
    cap = cv2.VideoCapture(0)  # 参数 0 表示默认摄像头

    if not cap.isOpened():
        print("无法打开摄像头")
        return

    print("按 'q' 退出程序...")

    # 设置 ROI 的位置和大小
    roi_x = 100  # ROI 的左上角 x 坐标
    roi_y = 100  # ROI 的左上角 y 坐标
    roi_width = 300  # ROI 的宽度
    roi_height = 300  # ROI 的高度

    # 用于存储边缘点
    edge_points = []

    while True:
        # 读取一帧
        ret, frame = cap.read()

        if not ret:
            print("无法读取帧")
            break

        # 绘制 ROI 边框（可选）
        cv2.rectangle(frame, (roi_x, roi_y), (roi_x + roi_width, roi_y + roi_height), (255, 0, 0), 2)

        # 裁剪 ROI 区域
        roi_frame = frame[roi_y:roi_y + roi_height, roi_x:roi_x + roi_width]

        # 将 ROI 区域转换为灰度图
        gray_roi_frame = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2GRAY)

        # 应用 Canny 边缘检测
        edges = cv2.Canny(gray_roi_frame, threshold1=100, threshold2=200)

        # 在 ROI 区域上绘制边缘
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 先用绿色绘制边缘
        cv2.drawContours(roi_frame, contours, -1, (0, 255, 0), 2)  # 绿色轮廓

        # 获取所有边缘点
        all_edge_points = []
        for contour in contours:
            for point in contour:
                all_edge_points.append(point[0])

        # 如果是第一次检测到边缘，初始化边缘点
        if not edge_points:
            edge_points = all_edge_points

        # 逐步绘制红色边缘
        for i in range(len(edge_points)):
            cv2.circle(frame, (edge_points[i][0] + roi_x, edge_points[i][1] + roi_y), 1, (0, 0, 255), -1)

        # 更新边缘点
        edge_points = all_edge_points

        # 将处理后的 ROI 区域放回原图
        frame[roi_y:roi_y + roi_height, roi_x:roi_x + roi_width] = roi_frame

        # 显示结果
        cv2.imshow('Edges on ROI', frame)

        # 按下 'q' 键退出
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # 释放摄像头资源并关闭窗口
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()