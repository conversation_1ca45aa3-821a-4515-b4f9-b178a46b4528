import cv2
import numpy as np
import time

# 初始化摄像头
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
cap.set(cv2.CAP_PROP_FPS, 30)  # 尝试设置更高的帧率

if not cap.isOpened():
    print("无法打开摄像头！")
    exit()

# 创建参数调节窗口
cv2.namedWindow("Parameters")
cv2.resizeWindow("Parameters", 640, 300)

# 初始化参数
params = {
    "threshold": 50,
    "min_area": 500,
    "max_area": 30000,
    "blur_size": 5,
    "smooth_factor": 0.7,
    "dilation_size": 4,
    "erosion_size": 1,
    "aspect_min": 0.7,
    "aspect_max": 1.5,
    "solidity_min": 0.7,
    "kalman_gain": 0.8,
    "motion_threshold": 5,
    "history_weight": 0.8
}

# 创建滑动条
cv2.createTrackbar("Threshold", "Parameters", params["threshold"], 255, lambda x: None)
cv2.createTrackbar("Min Area", "Parameters", params["min_area"], 5000, lambda x: None)
cv2.createTrackbar("Max Area", "Parameters", params["max_area"], 100000, lambda x: None)
cv2.createTrackbar("Blur Size", "Parameters", params["blur_size"], 15, lambda x: None)
cv2.createTrackbar("Smooth Factor", "Parameters", int(params["smooth_factor"]*100), 100, lambda x: None)
cv2.createTrackbar("Dilation Size", "Parameters", params["dilation_size"], 10, lambda x: None)
cv2.createTrackbar("Erosion Size", "Parameters", params["erosion_size"], 10, lambda x: None)
cv2.createTrackbar("Aspect Min", "Parameters", int(params["aspect_min"]*100), 100, lambda x: None)
cv2.createTrackbar("Aspect Max", "Parameters", int(params["aspect_max"]*100), 200, lambda x: None)
cv2.createTrackbar("Solidity Min", "Parameters", int(params["solidity_min"]*100), 100, lambda x: None)
cv2.createTrackbar("Kalman Gain", "Parameters", int(params["kalman_gain"]*100), 100, lambda x: None)
cv2.createTrackbar("Motion Thresh", "Parameters", params["motion_threshold"], 50, lambda x: None)
cv2.createTrackbar("History Weight", "Parameters", int(params["history_weight"]*100), 100, lambda x: None)

print("开始实时检测，按 'q' 键退出...")
print("按 's' 键保存当前帧快照")
print("按 'r' 键重置参数")

# 用于FPS计算
prev_time = time.time()
fps = 0

# 用于运动估计
prev_frame = None
prev_points = None
prev_center = None

# 用于稳定中心点
stable_center = None
detection_history = []
frame_counter = 0

# 卡尔曼滤波器状态
kalman_state = None
kalman_covariance = np.eye(4) * 0.1  # 初始协方差矩阵

# 运动模型：恒定速度模型
F = np.array([[1, 0, 1, 0],
              [0, 1, 0, 1],
              [0, 0, 1, 0],
              [0, 0, 0, 1]])

# 测量矩阵
H = np.array([[1, 0, 0, 0],
              [0, 1, 0, 0]])

# 过程噪声协方差
Q = np.eye(4) * 0.01

# 测量噪声协方差
R = np.eye(2) * 10

def kalman_predict(state, covariance):
    """卡尔曼滤波器预测步骤"""
    predicted_state = F @ state
    predicted_covariance = F @ covariance @ F.T + Q
    return predicted_state, predicted_covariance

def kalman_update(state, covariance, measurement):
    """卡尔曼滤波器更新步骤"""
    # 计算卡尔曼增益
    S = H @ covariance @ H.T + R
    K = covariance @ H.T @ np.linalg.inv(S)
    
    # 更新状态估计
    innovation = measurement - H @ state
    updated_state = state + K @ innovation
    updated_covariance = (np.eye(4) - K @ H) @ covariance
    
    return updated_state, updated_covariance

def estimate_motion(current_frame, prev_frame, prev_points):
    """使用光流估计相机运动"""
    if prev_frame is None or prev_points is None or len(prev_points) < 10:
        return np.zeros(2), None
    
    # 转换为灰度图
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
    
    # 计算光流
    current_points, status, _ = cv2.calcOpticalFlowPyrLK(
        prev_gray, current_gray, prev_points, None,
        winSize=(21, 21), maxLevel=3,
        criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 30, 0.01)
    )
    
    # 过滤有效的点
    valid_prev = prev_points[status == 1]
    valid_curr = current_points[status == 1]
    
    if len(valid_prev) < 5:
        return np.zeros(2), None
    
    # 计算位移向量
    displacement = np.median(valid_curr - valid_prev, axis=0)
    
    # 返回位移和新特征点
    return displacement, valid_curr.reshape(-1, 1, 2)

def detect_features(frame):
    """检测特征点用于光流跟踪"""
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    corners = cv2.goodFeaturesToTrack(gray, maxCorners=100, qualityLevel=0.01, minDistance=10)
    return corners

while True:
    ret, frame = cap.read()
    if not ret:
        print("无法获取帧，退出...")
        break
    
    # 计算FPS
    current_time = time.time()
    fps = 1 / (current_time - prev_time)
    prev_time = current_time
    frame_counter += 1
    
    # 从滑动条获取当前参数值
    params["threshold"] = cv2.getTrackbarPos("Threshold", "Parameters")
    params["min_area"] = cv2.getTrackbarPos("Min Area", "Parameters")
    params["max_area"] = cv2.getTrackbarPos("Max Area", "Parameters")
    blur_size = cv2.getTrackbarPos("Blur Size", "Parameters")
    params["blur_size"] = blur_size if blur_size % 2 == 1 else blur_size + 1
    params["smooth_factor"] = cv2.getTrackbarPos("Smooth Factor", "Parameters") / 100.0
    params["dilation_size"] = cv2.getTrackbarPos("Dilation Size", "Parameters")
    params["erosion_size"] = cv2.getTrackbarPos("Erosion Size", "Parameters")
    params["aspect_min"] = cv2.getTrackbarPos("Aspect Min", "Parameters") / 100.0
    params["aspect_max"] = cv2.getTrackbarPos("Aspect Max", "Parameters") / 100.0
    params["solidity_min"] = cv2.getTrackbarPos("Solidity Min", "Parameters") / 100.0
    params["kalman_gain"] = cv2.getTrackbarPos("Kalman Gain", "Parameters") / 100.0
    params["motion_threshold"] = cv2.getTrackbarPos("Motion Thresh", "Parameters")
    params["history_weight"] = cv2.getTrackbarPos("History Weight", "Parameters") / 100.0
    
    # 1. 运动估计 - 使用光流跟踪相机运动
    motion_vector, current_points = estimate_motion(frame, prev_frame, prev_points)
    
    # 保存当前帧和特征点用于下一帧
    prev_frame = frame.copy()
    prev_points = current_points if current_points is not None else detect_features(frame)
    
    # 预处理
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # 应用高斯模糊
    if params["blur_size"] > 0:
        blurred = cv2.GaussianBlur(gray, (params["blur_size"], params["blur_size"]), 0)
    else:
        blurred = gray
    
    # 阈值处理
    _, thresh = cv2.threshold(blurred, params["threshold"], 255, cv2.THRESH_BINARY_INV)
    
    # 形态学操作
    kernel = np.ones((3, 3), np.uint8)
    if params["erosion_size"] > 0:
        thresh = cv2.erode(thresh, kernel, iterations=params["erosion_size"])
    if params["dilation_size"] > 0:
        thresh = cv2.dilate(thresh, kernel, iterations=params["dilation_size"])
    
    # 查找轮廓
    result = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 根据OpenCV版本提取轮廓
    if len(result) == 3:
        contours = result[1]  # OpenCV 4.x
    elif len(result) == 2:
        contours = result[0]  # OpenCV 3.x
    else:
        contours = result[0]  # OpenCV 2.x
    
    # 筛选黑色矩形轮廓
    best_contour = None
    max_solidity = 0
    current_center = None
    
    for cnt in contours:
        area = cv2.contourArea(cnt)
        
        # 面积过滤
        if area < params["min_area"] or area > params["max_area"]:
            continue
            
        # 外接矩形
        rect = cv2.minAreaRect(cnt)
        (_, _), (width, height), _ = rect
        
        # 计算长宽比 (确保不为零)
        if width > 0 and height > 0:
            aspect_ratio = max(width, height) / min(width, height)
        else:
            continue
            
        # 长宽比过滤
        if aspect_ratio < params["aspect_min"] or aspect_ratio > params["aspect_max"]:
            continue
            
        # 计算凸包和实心度
        hull = cv2.convexHull(cnt)
        hull_area = cv2.contourArea(hull)
        if hull_area > 0:
            solidity = float(area) / hull_area
        else:
            continue
            
        # 实心度过滤
        if solidity < params["solidity_min"]:
            continue
            
        # 选择实心度最高的轮廓
        if solidity > max_solidity:
            max_solidity = solidity
            best_contour = cnt
    
    # 处理检测到的轮廓
    if best_contour is not None:
        # 计算轮廓的矩和中心点
        M = cv2.moments(best_contour)
        if M["m00"] != 0:
            cX = int(M["m10"] / M["m00"])
            cY = int(M["m01"] / M["m00"])
            current_center = (cX, cY)
            
            # 初始化卡尔曼滤波器
            if kalman_state is None:
                kalman_state = np.array([cX, cY, 0, 0])
            
            # 卡尔曼滤波器预测
            kalman_state, kalman_covariance = kalman_predict(kalman_state, kalman_covariance)
            
            # 卡尔曼滤波器更新
            measurement = np.array([cX, cY])
            kalman_state, kalman_covariance = kalman_update(kalman_state, kalman_covariance, measurement)
            
            kalman_center = (int(kalman_state[0]), int(kalman_state[1]))
            
            # 添加到检测历史
            detection_history.append(kalman_center)
            if len(detection_history) > 10:
                detection_history.pop(0)
            
            # 多重稳定策略
            if stable_center is None:
                stable_center = kalman_center
            else:
                # 1. 加权历史平均
                hist_weight = params["history_weight"]
                weighted_x = hist_weight * stable_center[0] + (1 - hist_weight) * kalman_center[0]
                weighted_y = hist_weight * stable_center[1] + (1 - hist_weight) * kalman_center[1]
                
                # 2. 运动补偿
                if np.linalg.norm(motion_vector) > params["motion_threshold"]:
                    # 如果检测到显著运动，调整位置
                    weighted_x += motion_vector[0] * 0.5
                    weighted_y += motion_vector[1] * 0.5
                
                stable_center = (int(weighted_x), int(weighted_y))
            
            # 绘制轮廓和中心点
            cv2.drawContours(frame, [best_contour], -1, (0, 255, 0), 2)
            
            # 绘制外接矩形
            rect = cv2.minAreaRect(best_contour)
            box = cv2.boxPoints(rect)
            box = np.int0(box)
            cv2.drawContours(frame, [box], 0, (0, 255, 255), 2)
            
            # 绘制检测到的中心点（绿色）
            cv2.circle(frame, current_center, 5, (0, 255, 0), -1)
            
            # 绘制卡尔曼滤波后的中心点（黄色）
            cv2.circle(frame, kalman_center, 7, (0, 255, 255), -1)
            
            # 绘制稳定中心点（红色）
            cv2.circle(frame, stable_center, 10, (0, 0, 255), -1)
            
            # 显示中心点坐标
            cv2.putText(frame, f"Stable Center: ({stable_center[0]}, {stable_center[1]})", 
                       (stable_center[0] - 70, stable_center[1] - 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
            # 显示轮廓信息
            cv2.putText(frame, f"Area: {area}", (10, 90), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            cv2.putText(frame, f"Solidity: {max_solidity:.2f}", (10, 110), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
    
    # 未检测到轮廓时的处理
    else:
        # 使用卡尔曼滤波器预测
        if kalman_state is not None:
            kalman_state, kalman_covariance = kalman_predict(kalman_state, kalman_covariance)
            kalman_center = (int(kalman_state[0]), int(kalman_state[1]))
            
            # 应用运动补偿
            if np.linalg.norm(motion_vector) > params["motion_threshold"]:
                kalman_center = (int(kalman_center[0] + motion_vector[0]), 
                                int(kalman_center[1] + motion_vector[1]))
            
            # 使用历史稳定点
            if stable_center is not None:
                # 基于运动向量更新稳定点
                stable_center = (int(stable_center[0] + motion_vector[0]),
                                 int(stable_center[1] + motion_vector[1]))
                
                # 绘制预测的中心点（蓝色）
                cv2.circle(frame, stable_center, 10, (255, 0, 0), -1)
                cv2.putText(frame, f"Predicted: ({stable_center[0]}, {stable_center[1]})", 
                           (stable_center[0] - 70, stable_center[1] - 40),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
    
    # 在图像上显示FPS和参数
    cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    param_text = f"Thresh: {params['threshold']} | Min Area: {params['min_area']}"
    cv2.putText(frame, param_text, (10, 60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
    
    # 显示运动向量
    if motion_vector is not None:
        motion_text = f"Motion: ({motion_vector[0]:.1f}, {motion_vector[1]:.1f})"
        cv2.putText(frame, motion_text, (10, frame.shape[0] - 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 显示检测状态
    if current_center is not None:
        status = "Detected"
        color = (0, 255, 0)
    else:
        status = "Predicted"
        color = (255, 0, 0)
    
    cv2.putText(frame, status, (frame.shape[1] - 150, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
    
    # 显示结果
    cv2.imshow("Real-time Detection", frame)
    cv2.imshow("Threshold View", thresh)
    
    # 退出条件
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord('s'):
        cv2.imwrite("detection_snapshot.jpg", frame)
        print("已保存快照: detection_snapshot.jpg")
    elif key == ord('r'):
        # 重置参数
        cv2.setTrackbarPos("Threshold", "Parameters", 50)
        cv2.setTrackbarPos("Min Area", "Parameters", 500)
        cv2.setTrackbarPos("Max Area", "Parameters", 30000)
        cv2.setTrackbarPos("Blur Size", "Parameters", 5)
        cv2.setTrackbarPos("Smooth Factor", "Parameters", 70)
        cv2.setTrackbarPos("Dilation Size", "Parameters", 4)
        cv2.setTrackbarPos("Erosion Size", "Parameters", 1)
        cv2.setTrackbarPos("Aspect Min", "Parameters", 70)
        cv2.setTrackbarPos("Aspect Max", "Parameters", 150)
        cv2.setTrackbarPos("Solidity Min", "Parameters", 70)
        cv2.setTrackbarPos("Kalman Gain", "Parameters", 80)
        cv2.setTrackbarPos("Motion Thresh", "Parameters", 5)
        cv2.setTrackbarPos("History Weight", "Parameters", 80)
        print("参数已重置")

# 释放资源
cap.release()
cv2.destroyAllWindows()
print("程序已退出")