import cv2
import numpy as np
import math
import gc
import time
import serial
import struct

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        # 紫激光检测代码先注释，看后续需要而定
        return img, []  # 暂时返回空的激光点列表

# --------------------------- 串口通信类 ---------------------------
class SerialComm:
    """串口通信类 - 发送矩形中点坐标"""

    def __init__(self, port='/dev/ttyTHS0', baudrate=115200, enable=True):
        self.port = port
        self.baudrate = baudrate
        self.enable = enable
        self.connection = None

        # 指令类型定义
        self.CMD_RECT_CENTER = 0x02    # 矩形中心点坐标

    def init(self):
        """初始化串口连接"""
        if not self.enable:
            print("串口发送功能已禁用")
            return None

        try:
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"串口 {self.port} 已打开，波特率 {self.baudrate}")
            return self.connection
        except serial.SerialException as e:
            print(f"无法打开串口 {self.port}: {e}")
            self.connection = None
            return None

    def create_packet(self, cmd, data):
        """创建串口通信数据包

        数据包格式：[帧头2字节][指令1字节][数据长度1字节][数据N字节][校验和1字节]
        """
        header = bytes([0xAA, 0x55])  # 帧头
        cmd_byte = bytes([cmd])       # 指令类型
        data_len = len(data)
        len_byte = bytes([data_len])  # 数据长度

        # 校验和计算
        checksum = cmd + data_len
        for byte in data:
            checksum += byte
        checksum = checksum & 0xFF
        checksum_byte = bytes([checksum])

        return header + cmd_byte + len_byte + data + checksum_byte

    def send_rect_center(self, center_point):
        """发送矩形中心点坐标（通过0x02命令）"""
        if self.connection is None or not self.enable or center_point is None:
            return

        try:
            x = max(0, int(center_point[0]))
            y = max(0, int(center_point[1]))
            data = struct.pack('<HH', x, y)  # 小端字节序，2个16位无符号整数

            packet = self.create_packet(self.CMD_RECT_CENTER, data)
            self.connection.write(packet)

            current_time = time.time()
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 串口发送矩形中心点: ({x}, {y})")

        except Exception as e:
            print(f"发送矩形中心点数据时出错: {e}")

    def close(self):
        """关闭串口连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            print("串口已关闭")

# --------------------------- 虚拟按键类 ---------------------------
class VirtualButtons:
    def __init__(self):
        # 按键定义 [x, y, width, height, text, action]
        self.buttons = [
            [20, 180, 45, 20, "Center", "center"],    # 中心点模式
            [115, 180, 50, 20, "Circle", "circle"],   # 圆形模式
            [180, 180, 25, 20, "T-", "thresh_down"],  # 阈值减少
            [210, 180, 25, 20, "T+", "thresh_up"]     # 阈值增加
        ]
        
        # 键盘映射
        self.key_mapping = {
            ord('1'): "center",
            ord('2'): "circle",
            ord('-'): "thresh_down",
            ord('='): "thresh_up",
            ord('v'): "toggle_video",  # 切换视频显示
            ord('d'): "toggle_debug",  # 切换调试窗口
            ord('t'): "toggle_trackbar", # 切换滑动条窗口
            ord('q'): "quit"
        }
        
    def check_keyboard(self):
        """检查键盘输入"""
        key = cv2.waitKey(1) & 0xFF
        if key in self.key_mapping:
            return self.key_mapping[key]
        return None

    def draw_buttons(self, img, current_mode, threshold=46):
        """绘制虚拟按键和说明"""
        for button in self.buttons:
            x, y, w, h, text, action = button
            
            # 根据当前模式高亮按钮
            if (action == "center" and current_mode == "center") or \
               (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)  # 黄色高亮
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)    # 绿色阈值按键
                thickness = 2
            else:
                color = (255, 255, 255)  # 白色普通
                thickness = 2

            # 绘制按钮边框
            cv2.rectangle(img, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制按钮文字
            text_x = x + (w - len(text) * 4) // 2
            text_y = y + (h + 6) // 2
            cv2.putText(img, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # 显示当前阈值和键盘说明
        cv2.putText(img, f"Thresh: {threshold}", (180, 170), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        cv2.putText(img, "Keys: 1=Center 2=Circle -/+=Thresh V=Video D=Debug T=Trackbar Q=Quit", (10, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)

# --------------------------- 圆形轨迹点生成函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """在校正后的矩形内生成圆形轨迹点"""
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

# --------------------------- 透视变换工具函数 ---------------------------
def perspective_transform(pts, target_width, target_height):
    """对四边形进行透视变换"""
    # 顶点排序（左上→右上→右下→左下）
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)  # 逆矩阵用于映射回原图
    return M, M_inv, src_pts

# --------------------------- 可视化调试函数 ---------------------------
def create_threshold_trackbar():
    """创建阈值调节滑动条窗口"""
    cv2.namedWindow('Threshold Control', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('Threshold Control', 400, 200)

    # 创建滑动条
    cv2.createTrackbar('Binary Threshold', 'Threshold Control', 66, 255, lambda x: None)
    cv2.createTrackbar('Min Area', 'Threshold Control', 5, 100, lambda x: None)  # 500/100
    cv2.createTrackbar('Max Area', 'Threshold Control', 1000, 2000, lambda x: None)  # 100000/100

    # 创建控制面板背景
    control_panel = np.zeros((200, 400, 3), dtype=np.uint8)
    cv2.putText(control_panel, "Threshold Visualization Control", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(control_panel, "Adjust sliders to see real-time effects", (10, 60),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
    cv2.putText(control_panel, "Binary Threshold: Controls edge detection", (10, 90),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    cv2.putText(control_panel, "Min/Max Area: Controls rectangle size filter", (10, 110),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    cv2.putText(control_panel, "Press 'T' to toggle this window", (10, 140),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)
    cv2.imshow('Threshold Control', control_panel)

def get_trackbar_values():
    """获取滑动条的值"""
    try:
        binary_thresh = cv2.getTrackbarPos('Binary Threshold', 'Threshold Control')
        min_area = cv2.getTrackbarPos('Min Area', 'Threshold Control') * 100  # 恢复缩放
        max_area = cv2.getTrackbarPos('Max Area', 'Threshold Control') * 100  # 恢复缩放
        return binary_thresh, min_area, max_area
    except:
        return 66, 500, 100000  # 默认值

def create_debug_visualization(original, gray, binary, contours, quads, threshold):
    """创建调试可视化窗口"""
    # 调整图像大小用于显示
    display_size = (320, 240)

    # 原图
    original_small = cv2.resize(original, display_size)

    # 灰度图
    gray_small = cv2.resize(gray, display_size)
    gray_3ch = cv2.cvtColor(gray_small, cv2.COLOR_GRAY2BGR)

    # 二值化图
    binary_small = cv2.resize(binary, display_size)
    binary_3ch = cv2.cvtColor(binary_small, cv2.COLOR_GRAY2BGR)

    # 轮廓图
    contour_img = original.copy()
    cv2.drawContours(contour_img, contours, -1, (0, 255, 0), 2)
    contour_small = cv2.resize(contour_img, display_size)

    # 添加标题和信息
    cv2.putText(original_small, "Original", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(gray_3ch, "Grayscale", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(binary_3ch, f"Binary (T={threshold})", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    cv2.putText(contour_small, f"Contours ({len(contours)})", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    # 添加轮廓统计信息
    cv2.putText(binary_3ch, f"Found: {len(contours)}", (10, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
    cv2.putText(contour_small, f"Quads: {len(quads)}", (10, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

    # 组合显示
    top_row = np.hstack([original_small, gray_3ch])
    bottom_row = np.hstack([binary_3ch, contour_small])
    debug_combined = np.vstack([top_row, bottom_row])

    # 添加整体信息
    info_height = 60
    info_panel = np.zeros((info_height, debug_combined.shape[1], 3), dtype=np.uint8)
    cv2.putText(info_panel, f"Threshold Effect Visualization - Press D to toggle", (10, 25),
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    cv2.putText(info_panel, f"Adjust threshold to see changes in binary image and contour detection", (10, 45),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

    final_display = np.vstack([info_panel, debug_combined])

    return final_display

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    gc.disable()
    print("Jetson Nano版jiguangcar程序启动...")
    
    # 初始化摄像头 - 使用标准OpenCV
    cap = cv2.VideoCapture(0)  # 使用默认摄像头
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    if not cap.isOpened():
        print("无法打开摄像头")
        exit()
    
    # 初始化其他组件
    laser_detector = PurpleLaserDetector()
    buttons = VirtualButtons()
    current_mode = "center"  # 默认选择center模式
    show_video = True  # 视频显示开关，默认关闭
    video_window_created = False  # 跟踪视频窗口是否已创建
    show_debug = False  # 调试窗口显示开关
    show_trackbar = False  # 滑动条窗口显示开关

    # 初始化串口通信
    serial_comm = SerialComm(port='/dev/ttyTHS0', baudrate=115200, enable=True)
    serial_comm.init()
    
    # 核心参数
    min_contour_area = 500
    max_contour_area = 100000
    target_sides = 4
    binary_threshold = 66
    
    # 透视变换与圆形参数
    corrected_width = 200
    corrected_height = 150
    circle_radius = 50
    circle_num_points = 12
    
    # FPS计算
    fps = 0
    last_time = time.time()
    frame_count = 0
    fps_report_time = time.time()  # FPS报告时间
    fps_report_interval = 2.0      # 每2秒输出一次FPS

    print("程序运行中...")
    print("功能说明:")
    print("- 矩形检测: 自动检测画面中的矩形")
    print("- 串口发送: 通过0x02命令发送矩形中心点坐标")
    print("- 实时调参: 按T键开启滑动条调节参数")
    print("- 调试显示: 按D键查看处理过程")
    print("按键控制: V=视频 D=调试 T=调参 1/2=模式 Q=退出")
    
    while True:
        frame_count += 1
        
        # 读取摄像头图像
        ret, img_cv = cap.read()
        if not ret:
            print("无法读取摄像头数据")
            break
            
        output = img_cv.copy()
        
        # 处理键盘输入
        action = buttons.check_keyboard()
        if action:
            if action == "center":
                current_mode = "center"
                print("切换到中心点模式")
            elif action == "circle":
                current_mode = "circle"
                print("切换到圆形模式")
            elif action == "thresh_up":
                binary_threshold = min(255, binary_threshold + 3)
                print(f"阈值增加到: {binary_threshold}")
            elif action == "thresh_down":
                binary_threshold = max(1, binary_threshold - 3)
                print(f"阈值减少到: {binary_threshold}")
            elif action == "toggle_video":
                show_video = not show_video
                status = "开启" if show_video else "关闭"
                print(f"视频显示已{status}")
            elif action == "toggle_debug":
                show_debug = not show_debug
                status = "开启" if show_debug else "关闭"
                print(f"调试窗口已{status}")
                if not show_debug:
                    cv2.destroyWindow('Threshold Debug View')
            elif action == "toggle_trackbar":
                show_trackbar = not show_trackbar
                status = "开启" if show_trackbar else "关闭"
                print(f"滑动条窗口已{status}")
                if show_trackbar:
                    create_threshold_trackbar()
                else:
                    cv2.destroyWindow('Threshold Control')
            elif action == "quit":
                break
        
        # 计算FPS
        current_time = time.time()
        if current_time - last_time > 0:
            fps = 1.0 / (current_time - last_time)
        last_time = current_time

        # 定期输出FPS到控制台
        if current_time - fps_report_time >= fps_report_interval:
            print(f"当前帧率: {fps:.1f} FPS")
            fps_report_time = current_time
        
        # 从滑动条获取参数（如果启用）
        if show_trackbar:
            trackbar_thresh, trackbar_min_area, trackbar_max_area = get_trackbar_values()
            binary_threshold = trackbar_thresh
            min_contour_area = trackbar_min_area
            max_contour_area = trackbar_max_area

        # 1. 矩形检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 只检测面积最大的一个矩形
        inner_quads = []
        if quads:
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]

        # 显示调试可视化窗口
        if show_debug:
            debug_view = create_debug_visualization(img_cv, gray, binary, contours, quads, binary_threshold)
            cv2.imshow('Threshold Debug View', debug_view)

        # 2. 根据模式处理数据
        center_points = []
        all_circle_points = []

        for approx, area in inner_quads:
            # 提取顶点
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 绘制内框轮廓
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

            # 使用透视变换校正获得准确中心点
            M, M_inv, src_pts = perspective_transform(pts, corrected_width, corrected_height)
            if M_inv is not None:
                # 校正后矩形的真正几何中心
                corrected_center = (corrected_width//2, corrected_height//2)
                # 将校正后的中心点映射回原图
                center_np = np.array([[corrected_center]], dtype=np.float32)
                original_center = cv2.perspectiveTransform(center_np, M_inv)[0][0]
                cx, cy = int(original_center[0]), int(original_center[1])

                # 绘制校正后的中心点
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))
            else:
                # 备用方案：使用简单几何中心
                cx = int(np.mean(pts[:, 0]))
                cy = int(np.mean(pts[:, 1]))
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))

            # 圆形模式处理
            if current_mode == "circle":
                if M_inv is not None:
                    # 使用透视变换校正的圆轨迹
                    corrected_center = (corrected_width//2, corrected_height//2)
                    corrected_circle = generate_circle_points(
                        corrected_center, circle_radius, circle_num_points
                    )

                    # 将校正后的圆轨迹点映射回原图
                    corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                    original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                    original_points = [(int(x), int(y)) for x, y in original_points]
                    all_circle_points.extend(original_points)

                    # 绘制映射回原图的轨迹点（红色）
                    for (x, y) in original_points:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
                else:
                    # 备用方案：在原图上直接生成圆轨迹
                    simple_circle = generate_circle_points((cx, cy), 30, circle_num_points)
                    all_circle_points.extend(simple_circle)

                    # 绘制简单圆轨迹点（红色）
                    for (x, y) in simple_circle:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)

        # 3. 激光检测 (暂时注释)
        output, laser_points = laser_detector.detect(output)

        # 4. 数据输出和串口发送（只在识别到矩形时）
        if current_mode == "center":
            # 中心点模式：只有识别到矩形时才输出和发送
            if center_points:
                cx, cy = center_points[0]
                # 控制台输出
                print(f"R,{cx},{cy}")
                # 串口发送矩形中心点（通过0x02命令）
                serial_comm.send_rect_center((cx, cy))
            # 没有识别到矩形时不打印不发送
        elif current_mode == "circle":
            # 圆形模式：只有识别到矩形时才输出和发送
            if all_circle_points and center_points:
                circle_data = f"C,{len(all_circle_points)}"
                for (x, y) in all_circle_points:
                    circle_data += f",{x},{y}"
                print(circle_data)
                # 圆形模式发送圆心坐标
                cx, cy = center_points[0]
                serial_comm.send_rect_center((cx, cy))
            # 没有识别到矩形时不打印不发送

        # 5. 绘制目标点标记 - 紫色小十字标出(320,240)
        target_x, target_y = 320, 240
        cross_size = 5
        cv2.line(output, (target_x - cross_size, target_y), (target_x + cross_size, target_y), (255, 0, 255), 2)
        cv2.line(output, (target_x, target_y - cross_size), (target_x, target_y + cross_size), (255, 0, 255), 2)
        cv2.putText(output, f"({target_x},{target_y})", (target_x + 8, target_y - 8),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

        # 6. 绘制虚拟按键
        buttons.draw_buttons(output, current_mode, binary_threshold)

        # 7. 显示统计信息
        cv2.putText(output, f"FPS: {fps:.1f}", (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 显示当前模式
        mode_text = f"Mode: {current_mode.upper()}"
        cv2.putText(output, mode_text, (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        # 显示视频状态
        video_status = f"Video: {'ON' if show_video else 'OFF'}"
        video_color = (0, 255, 0) if show_video else (0, 0, 255)  # 绿色开启，红色关闭
        cv2.putText(output, video_status, (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, video_color, 1)

        # 显示调试状态
        debug_status = f"Debug: {'ON' if show_debug else 'OFF'}"
        debug_color = (0, 255, 0) if show_debug else (0, 0, 255)
        cv2.putText(output, debug_status, (10, 80),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, debug_color, 1)

        # 显示滑动条状态
        trackbar_status = f"Trackbar: {'ON' if show_trackbar else 'OFF'}"
        trackbar_color = (0, 255, 0) if show_trackbar else (0, 0, 255)
        cv2.putText(output, trackbar_status, (10, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, trackbar_color, 1)

        # 显示当前参数
        param_text = f"Thresh:{binary_threshold} Area:{min_contour_area}-{max_contour_area}"
        cv2.putText(output, param_text, (10, 120),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)

        # 显示检测统计
        stats_text = f"Contours:{len(contours)} Quads:{len(quads)}"
        cv2.putText(output, stats_text, (10, 140),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        # 显示串口状态
        serial_status = f"Serial: {'ON' if serial_comm.connection else 'OFF'}"
        serial_color = (0, 255, 0) if serial_comm.connection else (0, 0, 255)
        cv2.putText(output, serial_status, (10, 160),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, serial_color, 1)

        # 显示图像 (根据开关控制)
        if show_video:
            cv2.imshow('Jetson Nano Vision System', output)
            video_window_created = True
        else:
            # 如果关闭视频显示且窗口已创建，销毁窗口
            if video_window_created:
                cv2.destroyWindow('Jetson Nano Vision System')
                video_window_created = False

    # 清理资源
    cap.release()
    serial_comm.close()  # 关闭串口连接
    cv2.destroyAllWindows()
    print("程序已退出")
    print("可视化调试功能说明:")
    print("- 按 'D' 键开启/关闭调试窗口，查看阈值处理效果")
    print("- 按 'T' 键开启/关闭滑动条窗口，实时调节参数")
    print("- 调试窗口显示：原图、灰度图、二值化图、轮廓检测结果")
    print("- 滑动条可调节：二值化阈值、最小面积、最大面积")

