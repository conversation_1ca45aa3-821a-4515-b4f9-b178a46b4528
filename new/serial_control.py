import serial
import struct
import time

class SerialComm:
    """串口通信类 - 分离自23E.py"""
    
    def __init__(self, port='/dev/ttyTHS0', baudrate=115200, enable=True):
        self.port = port
        self.baudrate = baudrate
        self.enable = enable
        self.connection = None
        self.last_rect_send_time = 0
        self.rect_send_interval = 0.5  # 矩形发送间隔500ms
        
        # 指令类型定义
        self.CMD_RECT_VERTICES = 0x01  # 方框顶点坐标
        self.CMD_LASER_POINT = 0x02    # 激光点坐标

        # 电机控制命令定义
        self.init_motor_commands()

    def init_motor_commands(self):
        """初始化电机控制命令字节数组"""
        # 电机初始化命令
        self.RE = bytearray([0x01, 0xF3, 0xAB, 0x01, 0x00, 0x6B])

        # 电机速度控制命令
        self.SE = bytearray([0x01, 0xFD, 0x01, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x0C, 0x80, 0x00, 0x01, 0x6B])
        self.SE1 = bytearray([0x01, 0xF6, 0x01, 0x00, 0x3C, 0x00, 0x01, 0x6B])

        # 电机停止命令
        self.STP = bytearray([0x00, 0xFE, 0x98, 0x00, 0x6B])

        # 电机位置读取命令
        self.READ = bytearray([0x00, 0x36, 0x6B])
        self.READ_SPEED = bytearray([0x00, 0x35, 0x6B])

        # 电机清零命令
        self.CLEAR = bytearray([0x01, 0x0A, 0x6D, 0x6B])

        # 通用命令缓冲区
        self.TB = bytearray([0x00, 0xFF, 0x66, 0x6B])
        self.cmd = bytearray(16)  # 16字节命令缓冲区
        
    def init(self):
        """初始化串口连接"""
        if not self.enable:
            print("串口发送功能已禁用")
            return None
            
        try:
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"串口 {self.port} 已打开，波特率 {self.baudrate}")
            return self.connection
        except serial.SerialException as e:
            print(f"无法打开串口 {self.port}: {e}")
            self.connection = None
            return None
    
    def create_packet(self, cmd, data):
        """创建串口通信数据包
        
        数据包格式：[帧头2字节][指令1字节][数据长度1字节][数据N字节][校验和1字节]
        """
        header = bytes([0xAA, 0x55])  # 帧头
        cmd_byte = bytes([cmd])       # 指令类型
        data_len = len(data)
        len_byte = bytes([data_len])  # 数据长度
        
        # 校验和计算
        checksum = cmd + data_len
        for byte in data:
            checksum += byte
        checksum = checksum & 0xFF
        checksum_byte = bytes([checksum])
        
        return header + cmd_byte + len_byte + data + checksum_byte
    
    def send_rect_midpoints(self, midpoints):
        """发送矩形框四个中点坐标（带频率控制）"""
        if self.connection is None or not self.enable or midpoints is None:
            return
            
        current_time = time.time()
        if current_time - self.last_rect_send_time < self.rect_send_interval:
            return  # 还未到发送时间
            
        if len(midpoints) != 4:
            print(f"警告: 矩形中点数量不正确，期望4个，实际{len(midpoints)}个")
            return
            
        try:
            data = bytes()
            for (x, y) in midpoints:
                x = max(0, int(x))
                y = max(0, int(y))
                data += struct.pack('<HH', x, y)  # 小端字节序
                
            packet = self.create_packet(self.CMD_RECT_VERTICES, data)
            self.connection.write(packet)
            self.last_rect_send_time = current_time
            
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 串口发送矩形中点: {midpoints}")
            
        except Exception as e:
            print(f"发送矩形中点数据时出错: {e}")
    
    def send_red_laser_point(self, point):
        """发送红色激光点坐标（每帧发送，33.3 Hz）"""
        if self.connection is None or not self.enable or point is None:
            return
            
        try:
            x = max(0, int(point[0]))
            y = max(0, int(point[1]))
            data = struct.pack('<HH', x, y)
            
            packet = self.create_packet(self.CMD_LASER_POINT, data)
            self.connection.write(packet)
            
            current_time = time.time()
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 串口发送红色激光点: ({x}, {y})")
            
        except Exception as e:
            print(f"发送激光点数据时出错: {e}")
    
    def close(self):
        """关闭串口连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            print("串口已关闭")

    # ========================= 电机控制功能 =========================

    def delay_ms(self, ms):
        """延时函数（毫秒）"""
        time.sleep(ms / 1000.0)

    def abs_value(self, x):
        """绝对值函数"""
        return abs(x)

    def send_motor_command(self, command, length, description=""):
        """发送电机控制命令的通用函数"""
        if self.connection is None or not self.enable:
            return False

        try:
            self.connection.write(command[:length])
            if description:
                print(f"电机命令发送: {description}")
            return True
        except Exception as e:
            print(f"发送电机命令时出错: {e}")
            return False

    def motor_init(self):
        """电机初始化"""
        print("开始电机初始化...")

        # 发送第一个初始化命令
        self.RE[0] = 0x01
        self.send_motor_command(self.RE, 6, "电机初始化命令1")
        self.delay_ms(10)

        # 发送第二个初始化命令
        self.RE[0] = 0x02
        self.send_motor_command(self.RE, 6, "电机初始化命令2")
        self.delay_ms(10)

        # 停止电机
        self.motor_stop()
        print("电机初始化完成")

    def motor_speed(self, motor_id, turn_direction, speed, acceleration, sync=1):
        """
        电机速度控制
        :param motor_id: 电机ID (1或2)
        :param turn_direction: 转向 (0=正转, 1=反转)
        :param speed: 速度 (0-65535)
        :param acceleration: 加速度 (0-255)
        :param sync: 同步标志 (0或1)
        """
        if not (1 <= motor_id <= 2):
            print(f"错误: 电机ID必须为1或2，当前值: {motor_id}")
            return False

        if not (0 <= turn_direction <= 1):
            print(f"错误: 转向必须为0或1，当前值: {turn_direction}")
            return False

        if not (0 <= speed <= 65535):
            print(f"错误: 速度必须在0-65535范围内，当前值: {speed}")
            return False

        if not (0 <= acceleration <= 255):
            print(f"错误: 加速度必须在0-255范围内，当前值: {acceleration}")
            return False

        # 设置命令参数
        self.SE1[0] = motor_id
        self.SE1[2] = turn_direction
        self.SE1[3] = (speed >> 8) & 0xFF  # 高字节
        self.SE1[4] = speed & 0xFF         # 低字节
        self.SE1[5] = acceleration
        self.SE1[6] = sync

        success = self.send_motor_command(self.SE1, 8,
            f"电机{motor_id}速度控制: 方向={turn_direction}, 速度={speed}, 加速度={acceleration}")

        if success:
            self.delay_ms(10)
        return success

    def motor_stop(self):
        """停止所有电机"""
        success = self.send_motor_command(self.STP, 5, "停止所有电机")
        if success:
            self.delay_ms(10)
        return success

    def motor_clear_position(self, motor_id):
        """
        清零单个电机位置
        :param motor_id: 电机ID (1或2)
        """
        if not (1 <= motor_id <= 2):
            print(f"错误: 电机ID必须为1或2，当前值: {motor_id}")
            return False

        # 设置电机ID
        self.CLEAR[0] = motor_id
        success = self.send_motor_command(self.CLEAR, 4, f"清零电机{motor_id}位置")

        if success:
            self.delay_ms(10)
        return success

    def motor_clear_all_position(self):
        """清零所有电机位置"""
        print("开始清零所有电机位置...")

        # 清零电机1
        success1 = self.motor_clear_position(1)

        # 清零电机2
        success2 = self.motor_clear_position(2)

        if success1 and success2:
            print("所有电机位置清零完成")
            return True
        else:
            print("电机位置清零失败")
            return False

    def motor_read_distance(self, motor_id):
        """
        读取电机位置距离
        :param motor_id: 电机ID (1或2)
        """
        if not (1 <= motor_id <= 2):
            print(f"错误: 电机ID必须为1或2，当前值: {motor_id}")
            return False

        # 设置电机ID
        self.READ[0] = motor_id
        success = self.send_motor_command(self.READ, 3, f"读取电机{motor_id}位置")

        if success:
            self.delay_ms(10)
        return success

    def motor_read_speed(self, motor_id):
        """
        读取电机速度
        :param motor_id: 电机ID (1或2)
        """
        if not (1 <= motor_id <= 2):
            print(f"错误: 电机ID必须为1或2，当前值: {motor_id}")
            return False

        # 设置电机ID
        self.READ_SPEED[0] = motor_id
        success = self.send_motor_command(self.READ_SPEED, 3, f"读取电机{motor_id}速度")

        if success:
            self.delay_ms(10)
        return success

    # ========================= 高级电机控制功能 =========================

    def motor_move_forward(self, speed=1000, acceleration=50):
        """电机向前移动（两个电机同向转动）"""
        print(f"电机向前移动: 速度={speed}, 加速度={acceleration}")
        success1 = self.motor_speed(1, 0, speed, acceleration)  # 电机1正转
        success2 = self.motor_speed(2, 0, speed, acceleration)  # 电机2正转
        return success1 and success2

    def motor_move_backward(self, speed=1000, acceleration=50):
        """电机向后移动（两个电机反向转动）"""
        print(f"电机向后移动: 速度={speed}, 加速度={acceleration}")
        success1 = self.motor_speed(1, 1, speed, acceleration)  # 电机1反转
        success2 = self.motor_speed(2, 1, speed, acceleration)  # 电机2反转
        return success1 and success2

    def motor_turn_left(self, speed=1000, acceleration=50):
        """电机左转（电机1反转，电机2正转）"""
        print(f"电机左转: 速度={speed}, 加速度={acceleration}")
        success1 = self.motor_speed(1, 1, speed, acceleration)  # 电机1反转
        success2 = self.motor_speed(2, 0, speed, acceleration)  # 电机2正转
        return success1 and success2

    def motor_turn_right(self, speed=1000, acceleration=50):
        """电机右转（电机1正转，电机2反转）"""
        print(f"电机右转: 速度={speed}, 加速度={acceleration}")
        success1 = self.motor_speed(1, 0, speed, acceleration)  # 电机1正转
        success2 = self.motor_speed(2, 1, speed, acceleration)  # 电机2反转
        return success1 and success2

    # ========================= PID控制器类 =========================

class PIDController:
    """PID控制器类"""

    def __init__(self, kp=0.0, ki=0.0, kd=0.0, target=0.0, limit=100.0):
        # PID参数
        self.kp = kp          # 比例系数
        self.ki = ki          # 积分系数
        self.kd = kd          # 微分系数
        self.target = target  # 目标值
        self.limit = limit    # 输出限幅值

        # PID状态变量
        self.current = 0.0        # 当前值
        self.out = 0.0           # 输出值
        self.error = 0.0         # 当前误差
        self.last_error = 0.0    # 上次误差
        self.last2_error = 0.0   # 上上次误差
        self.last_out = 0.0      # 上次输出
        self.integral = 0.0      # 积分累加
        self.p_out = 0.0         # 比例输出
        self.i_out = 0.0         # 积分输出
        self.d_out = 0.0         # 微分输出

    def set_target(self, target):
        """设置目标值"""
        self.target = target

    def set_params(self, kp, ki, kd):
        """设置PID参数"""
        self.kp = kp
        self.ki = ki
        self.kd = kd

    def set_limit(self, limit):
        """设置输出限幅"""
        self.limit = limit

    def limit_integral(self, min_val, max_val):
        """限制积分项"""
        if self.integral > max_val:
            self.integral = max_val
        elif self.integral < min_val:
            self.integral = min_val

    def calculate_positional(self, current):
        """位置式PID计算"""
        self.current = current
        self.error = self.target - self.current

        # 比例项
        self.p_out = self.kp * self.error

        # 积分项
        self.integral += self.error
        self.i_out = self.ki * self.integral

        # 微分项
        self.d_out = self.kd * (self.error - self.last_error)

        # PID输出
        self.out = self.p_out + self.i_out + self.d_out

        # 输出限幅
        if self.out > self.limit:
            self.out = self.limit
        elif self.out < -self.limit:
            self.out = -self.limit

        # 更新历史值
        self.last2_error = self.last_error
        self.last_error = self.error
        self.last_out = self.out

        return self.out

class PIDParams:
    """PID参数配置类"""

    def __init__(self, kp=1.0, ki=0.0, kd=0.0, sample_time=0.01,
                 out_min=-99.0, out_max=99.0, i_min=-80.0, i_max=80.0, deadzone=2):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.sample_time = sample_time
        self.out_min = out_min
        self.out_max = out_max
        self.i_min = i_min
        self.i_max = i_max
        self.deadzone = deadzone

class MotorController:
    """电机控制器类 - 集成PID控制和电机驱动"""

    def __init__(self, serial_comm):
        self.serial_comm = serial_comm

        # 电机参数
        self.MOTOR_MAX_SPEED = 40

        # 目标坐标和当前坐标
        self.target_x = 0
        self.target_y = 0
        self.current_x = 0
        self.current_y = 0

        # 电机输出值
        self.motor_x = 0
        self.motor_y = 0

        # PID参数配置
        self.pid_params_x = PIDParams(
            kp=1.4, ki=0.000, kd=0.02,
            sample_time=0.01, out_min=-99.0, out_max=99.0,
            i_min=-80.0, i_max=80.0, deadzone=2
        )

        self.pid_params_y = PIDParams(
            kp=1.4, ki=0.000, kd=0.015,
            sample_time=0.01, out_min=-99.0, out_max=99.0,
            i_min=-80.0, i_max=80.0, deadzone=2
        )

        # 初始化PID控制器
        self.pid_x = PIDController(
            self.pid_params_x.kp, self.pid_params_x.ki, self.pid_params_x.kd,
            float(self.current_x), self.pid_params_x.out_max
        )

        self.pid_y = PIDController(
            self.pid_params_y.kp, self.pid_params_y.ki, self.pid_params_y.kd,
            float(self.current_y), self.pid_params_y.out_max
        )

    def constrain(self, value, min_val, max_val):
        """限制数值范围"""
        return max(min_val, min(max_val, value))

    def motor_set_speed(self, x_percent, y_percent):
        """
        设置电机速度（百分比控制）
        :param x_percent: X轴速度百分比 (-100 到 100)
        :param y_percent: Y轴速度百分比 (-100 到 100)
        """
        # 限制百分比范围
        x_percent = self.constrain(x_percent, -100, 100)
        y_percent = self.constrain(y_percent, -100, 100)

        # 设置X轴方向和速度
        if x_percent >= 0:
            x_dir = 0  # CW方向
        else:
            x_dir = 1  # CCW方向
            x_percent = -x_percent  # 取绝对值

        # 设置Y轴方向和速度
        if y_percent >= 0:
            y_dir = 0  # CW方向
        else:
            y_dir = 1  # CCW方向
            y_percent = -y_percent  # 取绝对值

        # 计算实际速度值(百分比转换为RPM)
        x_speed = int((x_percent * self.MOTOR_MAX_SPEED) / 100)
        y_speed = int((y_percent * self.MOTOR_MAX_SPEED) / 100)

        # 发送电机控制命令
        success1 = self.serial_comm.motor_speed(0x01, x_dir, x_speed, 0, 0)
        success2 = self.serial_comm.motor_speed(0x02, y_dir, y_speed, 0, 0)

        return success1 and success2

    def read_place(self, x, y):
        """读取当前位置"""
        self.current_x = x
        self.current_y = y

    def pid_target(self, x, y):
        """设置PID目标位置"""
        self.target_x = x
        self.target_y = y

        # 设置PID目标值
        self.pid_x.set_target(float(self.target_x))
        self.pid_y.set_target(float(self.target_y))

    def laser_open(self):
        """打开激光"""
        print("激光开启")
        # 这里可以添加GPIO控制代码

    def laser_close(self):
        """关闭激光"""
        print("激光关闭")
        # 这里可以添加GPIO控制代码

    def laser_set(self):
        """激光脉冲（开启20ms后关闭）"""
        print("激光脉冲")
        self.laser_open()
        self.serial_comm.delay_ms(20)
        self.laser_close()

    def app_pid_calc(self):
        """PID计算和电机控制"""
        error_x = self.target_x - self.current_x
        error_y = self.target_y - self.current_y

        # 死区判断：到达目标点则停止并触发激光
        if (abs(error_x) <= self.pid_params_x.deadzone and
            abs(error_y) <= self.pid_params_y.deadzone):

            # 清空积分避免残留
            self.pid_x.integral = 0
            self.pid_y.integral = 0

            # 停止电机并触发激光
            self.serial_comm.motor_stop()
            self.laser_set()
            print("到达目标点，停止电机并触发激光")
            return

        # PID计算
        output_x = self.pid_x.calculate_positional(float(self.current_x))
        self.pid_x.limit_integral(self.pid_params_x.i_min, self.pid_params_x.i_max)

        output_y = self.pid_y.calculate_positional(float(self.current_y))
        self.pid_y.limit_integral(self.pid_params_y.i_min, self.pid_params_y.i_max)

        # 输出限幅
        output_x = self.constrain(output_x, self.pid_params_x.out_min, self.pid_params_x.out_max)
        output_y = self.constrain(output_y, self.pid_params_y.out_min, self.pid_params_y.out_max)

        # 电机输出
        self.motor_x = int(output_x)
        self.motor_y = int(output_y)

        # 调试信息输出到控制台
        print(f"误差X={error_x} Y={error_y} 输出X={self.motor_x} Y={self.motor_y} "
              f"目标X={self.target_x} Y={self.target_y}")

        # 控制电机
        self.motor_set_speed(self.motor_x, -self.motor_y)

    def read_location_debug(self):
        """读取当前位置（调试信息）"""
        print(f"Now: X={self.current_x}, Y={self.current_y}")

    def init_pid(self):
        """初始化PID控制器"""
        print("初始化PID控制器...")

        # 重新初始化X轴PID控制器
        self.pid_x = PIDController(
            self.pid_params_x.kp, self.pid_params_x.ki, self.pid_params_x.kd,
            float(self.current_x), self.pid_params_x.out_max
        )

        # 重新初始化Y轴PID控制器
        self.pid_y = PIDController(
            self.pid_params_y.kp, self.pid_params_y.ki, self.pid_params_y.kd,
            float(self.current_y), self.pid_params_y.out_max
        )

        print("PID控制器初始化完成")

    def update_pid_params(self, axis, kp=None, ki=None, kd=None):
        """更新PID参数"""
        if axis.lower() == 'x':
            if kp is not None:
                self.pid_params_x.kp = kp
                self.pid_x.kp = kp
            if ki is not None:
                self.pid_params_x.ki = ki
                self.pid_x.ki = ki
            if kd is not None:
                self.pid_params_x.kd = kd
                self.pid_x.kd = kd
            print(f"X轴PID参数更新: Kp={self.pid_params_x.kp}, Ki={self.pid_params_x.ki}, Kd={self.pid_params_x.kd}")

        elif axis.lower() == 'y':
            if kp is not None:
                self.pid_params_y.kp = kp
                self.pid_y.kp = kp
            if ki is not None:
                self.pid_params_y.ki = ki
                self.pid_y.ki = ki
            if kd is not None:
                self.pid_params_y.kd = kd
                self.pid_y.kd = kd
            print(f"Y轴PID参数更新: Kp={self.pid_params_y.kp}, Ki={self.pid_params_y.ki}, Kd={self.pid_params_y.kd}")

    def get_status(self):
        """获取当前状态信息"""
        return {
            'current_pos': (self.current_x, self.current_y),
            'target_pos': (self.target_x, self.target_y),
            'motor_output': (self.motor_x, self.motor_y),
            'pid_x_params': (self.pid_params_x.kp, self.pid_params_x.ki, self.pid_params_x.kd),
            'pid_y_params': (self.pid_params_y.kp, self.pid_params_y.ki, self.pid_params_y.kd)
        }