import cv2
import numpy as np
import math
import gc
import time
import serial

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        # 紫激光检测代码先注释，看后续需要而定
        return img, []  # 暂时返回空的激光点列表

# --------------------------- 虚拟按键类 ---------------------------
class VirtualButtons:
    def __init__(self):
        # 按键定义 [x, y, width, height, text, action]
        self.buttons = [
            [20, 180, 45, 20, "Center", "center"],    # 中心点模式
            [115, 180, 50, 20, "Circle", "circle"],   # 圆形模式
            [180, 180, 25, 20, "T-", "thresh_down"],  # 阈值减少
            [210, 180, 25, 20, "T+", "thresh_up"]     # 阈值增加
        ]
        
        # 键盘映射
        self.key_mapping = {
            ord('1'): "center",
            ord('2'): "circle",
            ord('-'): "thresh_down",
            ord('='): "thresh_up",
            ord('v'): "toggle_video",  # 切换视频显示
            ord('q'): "quit"
        }
        
    def check_keyboard(self):
        """检查键盘输入"""
        key = cv2.waitKey(1) & 0xFF
        if key in self.key_mapping:
            return self.key_mapping[key]
        return None

    def draw_buttons(self, img, current_mode, threshold=46):
        """绘制虚拟按键和说明"""
        for button in self.buttons:
            x, y, w, h, text, action = button
            
            # 根据当前模式高亮按钮
            if (action == "center" and current_mode == "center") or \
               (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)  # 黄色高亮
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)    # 绿色阈值按键
                thickness = 2
            else:
                color = (255, 255, 255)  # 白色普通
                thickness = 2

            # 绘制按钮边框
            cv2.rectangle(img, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制按钮文字
            text_x = x + (w - len(text) * 4) // 2
            text_y = y + (h + 6) // 2
            cv2.putText(img, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # 显示当前阈值和键盘说明
        cv2.putText(img, f"Thresh: {threshold}", (180, 170), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        cv2.putText(img, "Keys: 1=Center 2=Circle -/+=Thresh V=Video Q=Quit", (10, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)

# --------------------------- 圆形轨迹点生成函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """在校正后的矩形内生成圆形轨迹点"""
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

# --------------------------- 透视变换工具函数 ---------------------------
def perspective_transform(pts, target_width, target_height):
    """对四边形进行透视变换"""
    # 顶点排序（左上→右上→右下→左下）
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)  # 逆矩阵用于映射回原图
    return M, M_inv, src_pts

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    gc.disable()
    print("Jetson Nano版jiguangcar程序启动...")
    
    # 初始化摄像头 - 使用标准OpenCV
    cap = cv2.VideoCapture(0)  # 使用默认摄像头
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    if not cap.isOpened():
        print("无法打开摄像头")
        exit()
    
    # 初始化其他组件
    laser_detector = PurpleLaserDetector()
    buttons = VirtualButtons()
    current_mode = "center"  # 默认选择center模式
    show_video = False  # 视频显示开关，默认关闭
    video_window_created = False  # 跟踪视频窗口是否已创建
    
    # 核心参数
    min_contour_area = 500
    max_contour_area = 70000
    target_sides = 4
    binary_threshold = 66
    
    # 透视变换与圆形参数
    corrected_width = 200
    corrected_height = 150
    circle_radius = 50
    circle_num_points = 12
    
    # FPS计算
    fps = 0
    last_time = time.time()
    frame_count = 0
    fps_report_time = time.time()  # FPS报告时间
    fps_report_interval = 2.0      # 每2秒输出一次FPS

    print("程序运行中... 按V键开启视频显示，按Q键退出")
    
    while True:
        frame_count += 1
        
        # 读取摄像头图像
        ret, img_cv = cap.read()
        if not ret:
            print("无法读取摄像头数据")
            break
            
        output = img_cv.copy()
        
        # 处理键盘输入
        action = buttons.check_keyboard()
        if action:
            if action == "center":
                current_mode = "center"
                print("切换到中心点模式")
            elif action == "circle":
                current_mode = "circle"
                print("切换到圆形模式")
            elif action == "thresh_up":
                binary_threshold = min(255, binary_threshold + 3)
                print(f"阈值增加到: {binary_threshold}")
            elif action == "thresh_down":
                binary_threshold = max(1, binary_threshold - 3)
                print(f"阈值减少到: {binary_threshold}")
            elif action == "toggle_video":
                show_video = not show_video
                status = "开启" if show_video else "关闭"
                print(f"视频显示已{status}")
            elif action == "quit":
                break
        
        # 计算FPS
        current_time = time.time()
        if current_time - last_time > 0:
            fps = 1.0 / (current_time - last_time)
        last_time = current_time

        # 定期输出FPS到控制台
        if current_time - fps_report_time >= fps_report_interval:
            print(f"当前帧率: {fps:.1f} FPS")
            fps_report_time = current_time
        
        # 1. 矩形检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 只检测面积最大的一个矩形
        inner_quads = []
        if quads:
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]

        # 2. 根据模式处理数据
        center_points = []
        all_circle_points = []

        for approx, area in inner_quads:
            # 提取顶点
            pts = approx.reshape(4, 2).astype(np.float32)

            # 绘制内框轮廓
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

            # 计算直接几何中心（简单平均）
            simple_cx = int(np.mean(pts[:, 0]))
            simple_cy = int(np.mean(pts[:, 1]))

            # 使用透视变换校正获得准确中心点
            M, M_inv, src_pts = perspective_transform(pts, corrected_width, corrected_height)
            if M_inv is not None:
                # 校正后矩形的真正几何中心
                corrected_center = (corrected_width//2, corrected_height//2)
                # 将校正后的中心点映射回原图
                center_np = np.array([[corrected_center]], dtype=np.float32)
                original_center = cv2.perspectiveTransform(center_np, M_inv)[0][0]
                perspective_cx, perspective_cy = int(original_center[0]), int(original_center[1])

                # 打印两种方法的对比结果
                print(f"=== 中心点对比 ===")
                print(f"直接几何中心: ({simple_cx}, {simple_cy})")
                print(f"透视变换中心: ({perspective_cx}, {perspective_cy})")
                diff_x = abs(perspective_cx - simple_cx)
                diff_y = abs(perspective_cy - simple_cy)
                print(f"差异: X轴={diff_x}px, Y轴={diff_y}px")
                print(f"总偏移距离: {math.sqrt(diff_x**2 + diff_y**2):.1f}px")
                print("==================")

                # 绘制两个中心点进行对比
                cv2.circle(output, (perspective_cx, perspective_cy), 5, (255, 0, 0), -1)  # 蓝色：透视变换中心
                cv2.circle(output, (simple_cx, simple_cy), 3, (0, 255, 255), -1)  # 黄色：几何中心

                # 添加标注
                cv2.putText(output, "P", (perspective_cx+8, perspective_cy-8), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
                cv2.putText(output, "G", (simple_cx+8, simple_cy-8), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

                center_points.append((perspective_cx, perspective_cy))
            else:
                # 备用方案：使用简单几何中心
                print(f"透视变换失败，使用几何中心: ({simple_cx}, {simple_cy})")
                cv2.circle(output, (simple_cx, simple_cy), 5, (255, 0, 0), -1)
                center_points.append((simple_cx, simple_cy))

            # 圆形模式处理
            if current_mode == "circle":
                if M_inv is not None:
                    # 使用透视变换校正的圆轨迹
                    corrected_center = (corrected_width//2, corrected_height//2)
                    corrected_circle = generate_circle_points(
                        corrected_center, circle_radius, circle_num_points
                    )

                    # 将校正后的圆轨迹点映射回原图
                    corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                    original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                    original_points = [(int(x), int(y)) for x, y in original_points]
                    all_circle_points.extend(original_points)

                    # 绘制映射回原图的轨迹点（红色）
                    for (x, y) in original_points:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
                else:
                    # 备用方案：在原图上直接生成圆轨迹
                    simple_circle = generate_circle_points((simple_cx, simple_cy), 30, circle_num_points)
                    all_circle_points.extend(simple_circle)

                    # 绘制简单圆轨迹点（红色）
                    for (x, y) in simple_circle:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)

        # 3. 激光检测 (暂时注释)
        output, laser_points = laser_detector.detect(output)

        # 4. 数据输出 (替代串口发送)
        if current_mode == "center":
            # 中心点模式：输出单个中心点
            if center_points:
                cx, cy = center_points[0]
                print(f"R,{cx},{cy}")
            else:
                print("R,0,0")
        elif current_mode == "circle":
            # 圆形模式：输出圆的一圈坐标
            if all_circle_points:
                circle_data = f"C,{len(all_circle_points)}"
                for (x, y) in all_circle_points:
                    circle_data += f",{x},{y}"
                print(circle_data)

        # 5. 绘制目标点标记 - 紫色小十字标出(320,240)
        target_x, target_y = 320, 240
        cross_size = 5
        cv2.line(output, (target_x - cross_size, target_y), (target_x + cross_size, target_y), (255, 0, 255), 2)
        cv2.line(output, (target_x, target_y - cross_size), (target_x, target_y + cross_size), (255, 0, 255), 2)
        cv2.putText(output, f"({target_x},{target_y})", (target_x + 8, target_y - 8),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

        # 6. 绘制虚拟按键
        buttons.draw_buttons(output, current_mode, binary_threshold)

        # 7. 显示统计信息
        cv2.putText(output, f"FPS: {fps:.1f}", (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 显示当前模式
        mode_text = f"Mode: {current_mode.upper()}"
        cv2.putText(output, mode_text, (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        # 显示视频状态
        video_status = f"Video: {'ON' if show_video else 'OFF'}"
        video_color = (0, 255, 0) if show_video else (0, 0, 255)  # 绿色开启，红色关闭
        cv2.putText(output, video_status, (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, video_color, 1)

        # 显示中心点说明
        cv2.putText(output, "P=Perspective G=Geometric", (10, 80),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)

        # 显示图像 (根据开关控制)
        if show_video:
            cv2.imshow('Jetson Nano Vision System', output)
            video_window_created = True
        else:
            # 如果关闭视频显示且窗口已创建，销毁窗口
            if video_window_created:
                cv2.destroyWindow('Jetson Nano Vision System')
                video_window_created = False

    # 清理资源
    cap.release()
    cv2.destroyAllWindows()
    print("程序已退出")

