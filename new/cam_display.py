import cv2
from ultralytics import YOLO
import time
import serial
import struct

# 初始化摄像头
cap = cv2.VideoCapture("/dev/video0")
if not cap.isOpened():
    print("无法打开摄像头！")
    exit()

# 设置更高的分辨率以获得更清晰的图像
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)  # 增加宽度
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)  # 增加高度

# 测试帧捕获
success, test_frame = cap.read()
if success:
    cv2.imwrite("test_frame.jpg", test_frame)
    print("测试帧已保存为 test_frame.jpg - 请检查此图像是否正常")
else:
    print("无法从摄像头读取帧！")
    exit()

# 串口通信配置
class SerialComm:
    def __init__(self, port='/dev/ttyTHS0', baudrate=115200, enable=True):
        self.port = port
        self.baudrate = baudrate
        self.enable = enable
        self.connection = None
        self.CMD_CENTER_POINT = 0x02  # 交点坐标指令

    def init(self):
        if not self.enable:
            print("串口发送功能已禁用")
            return None
        try:
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"串口 {self.port} 已打开，波特率 {self.baudrate}")
            return self.connection
        except serial.SerialException as e:
            print(f"无法打开串口 {self.port}: {e}")
            self.connection = None
            return None

    def create_packet(self, cmd, data):
        header = bytes([0xAA, 0x55])  # 帧头
        cmd_byte = bytes([cmd])       # 指令类型
        data_len = len(data)
        len_byte = bytes([data_len])  # 数据长度

        # 校验和计算
        checksum = cmd + data_len
        for byte in data:
            checksum += byte
        checksum = checksum & 0xFF
        checksum_byte = bytes([checksum])

        return header + cmd_byte + len_byte + data + checksum_byte

    def send_center_point(self, point):
        if self.connection is None or not self.enable or point is None:
            return
        try:
            x = max(0, int(point[0]))
            y = max(0, int(point[1]))
            data = struct.pack('<HH', x, y)

            packet = self.create_packet(self.CMD_CENTER_POINT, data)
            self.connection.write(packet)

            current_time = time.time()
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 串口发送交点坐标: ({x}, {y})")

        except Exception as e:
            print(f"发送交点坐标时出错: {e}")

    def close(self):
        if self.connection:
            self.connection.close()
            self.connection = None
            print("串口已关闭")

# 初始化串口通信
serial_comm = SerialComm()
serial_comm.init()

# 加载模型 - 修正模型名称
model = YOLO("best.engine",task='detect')  # 使用正确的模型名称
print(f"加载模型: {model.names}")  # 打印可检测类别

# 创建可调整大小的窗口
# cv2.namedWindow("YOLOv8 Detection", cv2.WINDOW_NORMAL)
# cv2.resizeWindow("YOLOv8 Detection", 640, 480)  # 设置初始窗口大小

# 调试参数
conf_threshold = 0.45  # 置信度阈值
fps = 0
frame_count = 0
start_time = time.time()

while cap.isOpened():
    success, frame = cap.read()
    if not success:
        break
    
    frame_count += 1
    
    # YOLO推理
    results = model(
        frame, 
        device="cuda",
        conf=conf_threshold,
        verbose=False
    )
    
    # 创建带检测结果的帧
    if results[0].boxes.xyxy.numel() > 0:  # 如果有检测结果（关键帧）
        annotated_frame = results[0].plot()

        # 获取帧尺寸
        h, w = frame.shape[:2]

        # 计算对角线交点（画面中心）
        center_x, center_y = w // 2, h // 2

        # 绘制对角线
        cv2.line(annotated_frame, (0, 0), (w, h), (255, 0, 0), 2)  # 左上到右下
        cv2.line(annotated_frame, (w, 0), (0, h), (255, 0, 0), 2)  # 右上到左下

        # 标注交点坐标
        cv2.circle(annotated_frame, (center_x, center_y), 8, (0, 0, 255), -1)  # 红色圆点
        cv2.putText(annotated_frame, f"({center_x},{center_y})",
                   (center_x + 15, center_y - 15), cv2.FONT_HERSHEY_SIMPLEX,
                   0.6, (0, 0, 255), 2)  # 坐标文字

        # 通过串口发送交点坐标（每帧发送）
        serial_comm.send_center_point((center_x, center_y))

        # 打印检测信息
        for box in results[0].boxes:
            class_id = int(box.cls)
            confidence = float(box.conf)
            print(f"检测到: {model.names[class_id]} ({confidence:.2f}) - 交点坐标: ({center_x},{center_y}) - (fps: {fps:.1f})")
    else:
        annotated_frame = frame.copy()
        print(f"无检测结果 - {frame_count}")
    
    # 添加FPS信息到画面
    cv2.putText(annotated_frame, f"FPS: {fps:.1f}", (10, 30), 
                 cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    cv2.putText(annotated_frame, f"Conf: {conf_threshold:.2f}", (10, 70), 
            cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    
    # # 只显示一个窗口
    cv2.imshow("YOLO11 Detection", annotated_frame)
    
    # 计算FPS
    current_time = time.time()
    if frame_count % 5 == 0:
        fps = 5 / (current_time - start_time)
        start_time = current_time
    
    # 按键处理
    key = cv2.waitKey(1)
    if key == ord("q"):
        break
    elif key == ord("+"):  # 提高置信度阈值
        conf_threshold = min(conf_threshold + 0.05, 0.9)
        print(f"置信度阈值提高到: {conf_threshold:.2f}")
    elif key == ord("-"):  # 降低置信度阈值
        conf_threshold = max(conf_threshold - 0.05, 0.01)
        print(f"置信度阈值降低到: {conf_threshold:.2f}")

cap.release()
cv2.destroyAllWindows()
serial_comm.close()  # 关闭串口连接