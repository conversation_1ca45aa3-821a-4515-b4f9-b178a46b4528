import cv2
from ultralytics import YOLO
import time

# 初始化摄像头
cap = cv2.VideoCapture("/dev/video0")
if not cap.isOpened():
    print("无法打开摄像头！")
    exit()

# 设置更高的分辨率以获得更清晰的图像
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)  # 增加宽度
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)  # 增加高度

# 测试帧捕获
success, test_frame = cap.read()
if success:
    cv2.imwrite("test_frame.jpg", test_frame)
    print("测试帧已保存为 test_frame.jpg - 请检查此图像是否正常")
else:
    print("无法从摄像头读取帧！")
    exit()

# 加载模型 - 修正模型名称
model = YOLO("best.engine",task='detect')  # 使用正确的模型名称
print(f"加载模型: {model.names}")  # 打印可检测类别

# 创建可调整大小的窗口
# cv2.namedWindow("YOLOv8 Detection", cv2.WINDOW_NORMAL)
# cv2.resizeWindow("YOLOv8 Detection", 640, 480)  # 设置初始窗口大小

# 调试参数
conf_threshold = 0.45  # 置信度阈值
fps = 0
frame_count = 0
start_time = time.time()

while cap.isOpened():
    success, frame = cap.read()
    if not success:
        break
    
    frame_count += 1
    
    # YOLO推理
    results = model(
        frame, 
        device="cuda",
        conf=conf_threshold,
        verbose=False
    )
    
    # 创建带检测结果的帧
    if results[0].boxes.xyxy.numel() > 0:  # 如果有检测结果（关键帧）
        annotated_frame = results[0].plot()

        # 获取帧尺寸
        h, w = frame.shape[:2]

        # 计算对角线交点（画面中心）
        center_x, center_y = w // 2, h // 2

        # 绘制对角线
        cv2.line(annotated_frame, (0, 0), (w, h), (255, 0, 0), 2)  # 左上到右下
        cv2.line(annotated_frame, (w, 0), (0, h), (255, 0, 0), 2)  # 右上到左下

        # 标注交点坐标
        cv2.circle(annotated_frame, (center_x, center_y), 8, (0, 0, 255), -1)  # 红色圆点
        cv2.putText(annotated_frame, f"({center_x},{center_y})",
                   (center_x + 15, center_y - 15), cv2.FONT_HERSHEY_SIMPLEX,
                   0.6, (0, 0, 255), 2)  # 坐标文字

        # 打印检测信息
        for box in results[0].boxes:
            class_id = int(box.cls)
            confidence = float(box.conf)
            print(f"检测到: {model.names[class_id]} ({confidence:.2f}) - 交点坐标: ({center_x},{center_y}) - (fps: {fps:.1f})")
    else:
        annotated_frame = frame.copy()
        print(f"无检测结果 - {frame_count}")
    
    # 添加FPS信息到画面
    cv2.putText(annotated_frame, f"FPS: {fps:.1f}", (10, 30), 
                 cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    cv2.putText(annotated_frame, f"Conf: {conf_threshold:.2f}", (10, 70), 
            cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    
    # # 只显示一个窗口
    cv2.imshow("YOLO11 Detection", annotated_frame)
    
    # 计算FPS
    current_time = time.time()
    if frame_count % 5 == 0:
        fps = 5 / (current_time - start_time)
        start_time = current_time
    
    # 按键处理
    key = cv2.waitKey(1)
    if key == ord("q"):
        break
    elif key == ord("+"):  # 提高置信度阈值
        conf_threshold = min(conf_threshold + 0.05, 0.9)
        print(f"置信度阈值提高到: {conf_threshold:.2f}")
    elif key == ord("-"):  # 降低置信度阈值
        conf_threshold = max(conf_threshold - 0.05, 0.01)
        print(f"置信度阈值降低到: {conf_threshold:.2f}")

cap.release()
cv2.destroyAllWindows()