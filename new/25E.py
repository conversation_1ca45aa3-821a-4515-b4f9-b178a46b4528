import cv2
import numpy as np
import math
import gc
import time
import serial
import struct

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        # 紫激光检测代码先注释，看后续需要而定
        return img, []  # 暂时返回空的激光点列表

# --------------------------- 虚拟按键类 ---------------------------
class VirtualButtons:
    def __init__(self):
        # 按键定义 [x, y, width, height, text, action]
        self.buttons = [
            [20, 180, 45, 20, "Center", "center"],    # 中心点模式
            [115, 180, 50, 20, "Circle", "circle"],   # 圆形模式
            [180, 180, 25, 20, "T-", "thresh_down"],  # 阈值减少
            [210, 180, 25, 20, "T+", "thresh_up"]     # 阈值增加
        ]
        
        # 键盘映射
        self.key_mapping = {
            ord('1'): "center",
            ord('2'): "circle",
            ord('-'): "thresh_down",
            ord('='): "thresh_up",
            ord('v'): "toggle_video",  # 切换视频显示
            ord('d'): "toggle_debug",  # 切换调试窗口
            ord('t'): "toggle_trackbar", # 切换滑动条窗口
            ord('q'): "quit"
        }
        


    def draw_buttons(self, img, current_mode, threshold=46):
        """绘制虚拟按键和说明"""
        for button in self.buttons:
            x, y, w, h, text, action = button
            
            # 根据当前模式高亮按钮
            if (action == "center" and current_mode == "center") or \
               (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)  # 黄色高亮
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)    # 绿色阈值按键
                thickness = 2
            else:
                color = (255, 255, 255)  # 白色普通
                thickness = 2

            # 绘制按钮边框
            cv2.rectangle(img, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制按钮文字
            text_x = x + (w - len(text) * 4) // 2
            text_y = y + (h + 6) // 2
            cv2.putText(img, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # 显示当前阈值和键盘说明
        cv2.putText(img, f"Thresh: {threshold}", (180, 170), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        cv2.putText(img, "Keys: 1=Center 2=Circle -/+=Thresh V=Video D=Debug T=Trackbar Q=Quit", (10, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)

# --------------------------- 圆形轨迹点生成函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """在校正后的矩形内生成圆形轨迹点"""
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

# --------------------------- 串口通信类 ---------------------------
class SerialComm:
    """串口通信类 - 仿照serial_comm.py实现"""

    def __init__(self, port='/dev/ttyTHS0', baudrate=115200, enable=True):
        self.port = port
        self.baudrate = baudrate
        self.enable = enable
        self.connection = None
        self.last_send_time = 0
        self.send_interval = 0.1  # 发送间隔100ms

        # 指令类型定义
        self.CMD_CENTER_POINT = 0x02  # 矩形中心点坐标

    def init(self):
        """初始化串口连接"""
        if not self.enable:
            print("串口发送功能已禁用")
            return None

        try:
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"串口 {self.port} 已打开，波特率 {self.baudrate}")
            return self.connection
        except serial.SerialException as e:
            print(f"无法打开串口 {self.port}: {e}")
            self.connection = None
            return None

    def create_packet(self, cmd, data):
        """创建串口通信数据包

        数据包格式：[帧头2字节][指令1字节][数据长度1字节][数据N字节][校验和1字节]
        """
        header = bytes([0xAA, 0x55])  # 帧头
        cmd_byte = bytes([cmd])       # 指令类型
        data_len = len(data)
        len_byte = bytes([data_len])  # 数据长度

        # 校验和计算
        checksum = cmd + data_len
        for byte in data:
            checksum += byte
        checksum = checksum & 0xFF
        checksum_byte = bytes([checksum])

        return header + cmd_byte + len_byte + data + checksum_byte

    def send_center_point(self, point):
        """发送矩形中心点坐标（无频率限制，能发多快就多快）"""
        if self.connection is None or not self.enable or point is None:
            return

        try:
            x = max(0, int(point[0]))
            y = max(0, int(point[1]))
            data = struct.pack('<HH', x, y)  # 小端字节序，2个16位无符号整数

            packet = self.create_packet(self.CMD_CENTER_POINT, data)
            self.connection.write(packet)

            current_time = time.time()
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 串口发送中心点: ({x}, {y})")

        except Exception as e:
            print(f"发送中心点数据时出错: {e}")

    def close(self):
        """关闭串口连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            print("串口已关闭")

# --------------------------- 透视变换工具函数 ---------------------------
def perspective_transform(pts, target_width, target_height):
    """对四边形进行透视变换"""
    # 顶点排序（左上→右上→右下→左下）
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)  # 逆矩阵用于映射回原图
    return M, M_inv, src_pts

# --------------------------- 可视化调参系统 ---------------------------
class ParameterTuner:
    """可视化参数调节器"""

    def __init__(self):
        self.window_name = 'Parameter Tuner'
        self.trackbar_created = False
        self.show_tuner = False

        # 参数范围定义
        self.param_ranges = {
            'binary_threshold': (1, 255, 66),
            'min_area': (1, 50, 5),  # 实际值 = 滑动条值 * 100
            'max_area': (500, 2000, 1200),  # 实际值 = 滑动条值 * 100
            'epsilon_factor': (1, 10, 3),  # 实际值 = 滑动条值 / 100
            'min_aspect_ratio': (30, 100, 60),  # 实际值 = 滑动条值 / 100
            'max_aspect_ratio': (100, 300, 170)  # 实际值 = 滑动条值 / 100
        }

    def create_trackbars(self):
        """创建参数调节滑动条"""
        if self.trackbar_created:
            return

        cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(self.window_name, 500, 400)
        # 设置窗口位置（右侧）
        cv2.moveWindow(self.window_name, 700, 50)

        # 创建滑动条
        for param, (min_val, max_val, default) in self.param_ranges.items():
            cv2.createTrackbar(param, self.window_name, default, max_val, lambda x: None)
            cv2.setTrackbarMin(param, self.window_name, min_val)

        # 创建控制面板
        self.update_control_panel()
        self.trackbar_created = True
        print(f"✅ 调参窗口已创建: {self.window_name}")

    def update_control_panel(self):
        """更新控制面板显示"""
        panel = np.zeros((400, 500, 3), dtype=np.uint8)

        # 标题
        cv2.putText(panel, "Real-time Parameter Tuner", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        # 参数说明
        y_pos = 70
        descriptions = [
            "Binary Threshold: Edge detection sensitivity",
            "Min Area: Filter small objects (x100)",
            "Max Area: Filter large objects (x100)",
            "Epsilon Factor: Contour approximation (/100)",
            "Min Aspect Ratio: Min width/height (/100)",
            "Max Aspect Ratio: Max width/height (/100)"
        ]

        for desc in descriptions:
            cv2.putText(panel, desc, (10, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
            y_pos += 25

        # 当前参数值显示
        y_pos += 20
        cv2.putText(panel, "Current Values:", (10, y_pos),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        y_pos += 30

        if self.trackbar_created:
            params = self.get_parameters()
            for param, value in params.items():
                text = f"{param}: {value}"
                cv2.putText(panel, text, (10, y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
                y_pos += 20

        # 使用说明
        y_pos += 20
        instructions = [
            "Instructions:",
            "- Drag sliders to adjust parameters",
            "- See real-time effects in debug window",
            "- Press 'T' to toggle this window",
            "- Press 'D' to toggle debug view"
        ]

        for instruction in instructions:
            color = (255, 255, 0) if instruction == "Instructions:" else (255, 255, 255)
            cv2.putText(panel, instruction, (10, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            y_pos += 20

        cv2.imshow(self.window_name, panel)

    def get_parameters(self):
        """获取当前参数值"""
        if not self.trackbar_created:
            return {
                'binary_threshold': 66,
                'min_area': 500,
                'max_area': 120000,
                'epsilon_factor': 0.03,
                'min_aspect_ratio': 0.6,
                'max_aspect_ratio': 1.7
            }

        try:
            params = {}
            params['binary_threshold'] = cv2.getTrackbarPos('binary_threshold', self.window_name)
            params['min_area'] = cv2.getTrackbarPos('min_area', self.window_name) * 100
            params['max_area'] = cv2.getTrackbarPos('max_area', self.window_name) * 100
            params['epsilon_factor'] = cv2.getTrackbarPos('epsilon_factor', self.window_name) / 100.0
            params['min_aspect_ratio'] = cv2.getTrackbarPos('min_aspect_ratio', self.window_name) / 100.0
            params['max_aspect_ratio'] = cv2.getTrackbarPos('max_aspect_ratio', self.window_name) / 100.0
            return params
        except:
            return {
                'binary_threshold': 66,
                'min_area': 500,
                'max_area': 120000,
                'epsilon_factor': 0.03,
                'min_aspect_ratio': 0.6,
                'max_aspect_ratio': 1.7
            }

    def toggle(self):
        """切换显示状态"""
        self.show_tuner = not self.show_tuner
        if self.show_tuner:
            self.create_trackbars()
        else:
            cv2.destroyWindow(self.window_name)
            self.trackbar_created = False

    def update_display(self):
        """更新显示"""
        if self.show_tuner and self.trackbar_created:
            self.update_control_panel()

# --------------------------- 矩形质量检验函数 ---------------------------
def is_regular_rectangle(approx):
    """判断是否为规则矩形（多维度校验）"""
    # 1. 确保是凸多边形
    if not cv2.isContourConvex(approx):
        return False, "非凸多边形"

    # 2. 提取四个顶点（按顺序）
    pts = approx.reshape(4, 2).astype(np.float32)
    p0, p1, p2, p3 = pts[0], pts[1], pts[2], pts[3]

    # 3. 计算四条边的长度
    edge_lengths = [
        math.hypot(p1[0]-p0[0], p1[1]-p0[1]),  # 上边
        math.hypot(p2[0]-p1[0], p2[1]-p1[1]),  # 右边
        math.hypot(p3[0]-p2[0], p3[1]-p2[1]),  # 下边
        math.hypot(p0[0]-p3[0], p0[1]-p3[1])   # 左边
    ]
    top, right, bottom, left = edge_lengths

    # 4. 校验对边长度是否接近（允许±20%偏差）
    if not (0.8 <= top/bottom <= 1.2 and 0.8 <= left/right <= 1.2):
        return False, f"对边不等（上/下={top/bottom:.2f}, 左/右={left/right:.2f}"

    # 5. 计算四个角的角度（更严格的直角校验：85°~95°）
    angles = []
    for i in range(4):
        p_prev = pts[i]
        p_curr = pts[(i+1)%4]
        p_next = pts[(i+2)%4]
        # 计算向量
        v1 = [p_curr[0]-p_prev[0], p_curr[1]-p_prev[1]]
        v2 = [p_next[0]-p_curr[0], p_next[1]-p_curr[1]]
        # 计算夹角（度）
        dot = v1[0]*v2[0] + v1[1]*v2[1]
        det = v1[0]*v2[1] - v1[1]*v2[0]
        angle = abs(math.degrees(math.atan2(det, dot)))
        angles.append(angle)

    if not all(85 <= angle <= 95 for angle in angles):
        return False, f"角度异常 {[round(a,1) for a in angles]}"

    # 所有条件通过
    return True, "规则矩形"

def create_enhanced_debug_view(original, gray, binary, contours, quads, params, stats):
    """创建增强的调试视图"""
    # 图像尺寸
    display_size = (300, 225)

    # 原始图像
    original_small = cv2.resize(original, display_size)

    # 灰度图像
    gray_small = cv2.resize(gray, display_size)
    gray_3ch = cv2.cvtColor(gray_small, cv2.COLOR_GRAY2BGR)

    # 二值化图像
    binary_small = cv2.resize(binary, display_size)
    binary_3ch = cv2.cvtColor(binary_small, cv2.COLOR_GRAY2BGR)

    # 轮廓检测结果
    contour_img = original.copy()
    # 绘制所有轮廓（绿色）
    cv2.drawContours(contour_img, contours, -1, (0, 255, 0), 1)
    # 绘制符合条件的四边形（红色）
    for quad, _ in quads:
        cv2.drawContours(contour_img, [quad], -1, (0, 0, 255), 2)
    contour_small = cv2.resize(contour_img, display_size)

    # 添加标题和参数信息
    cv2.putText(original_small, "Original", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(gray_3ch, "Grayscale", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    # 二值化图像显示阈值
    cv2.putText(binary_3ch, f"Binary T={params['binary_threshold']}", (10, 25),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    cv2.putText(binary_3ch, f"White pixels: {stats['white_pixels']}", (10, 210),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)

    # 轮廓检测结果显示统计
    cv2.putText(contour_small, f"Contours: {stats['total_contours']}", (10, 25),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    cv2.putText(contour_small, f"Area filtered: {stats['area_filtered']}", (10, 45),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)
    cv2.putText(contour_small, f"Quads: {stats['quads_found']}", (10, 65),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)
    cv2.putText(contour_small, f"Valid rects: {stats['valid_rectangles']}", (10, 85),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

    # 组合显示
    top_row = np.hstack([original_small, gray_3ch])
    bottom_row = np.hstack([binary_3ch, contour_small])
    debug_combined = np.vstack([top_row, bottom_row])

    # 添加参数信息面板
    info_height = 100
    info_panel = np.zeros((info_height, debug_combined.shape[1], 3), dtype=np.uint8)

    # 显示当前参数
    y_pos = 20
    param_texts = [
        f"Threshold: {params['binary_threshold']} | Min Area: {params['min_area']} | Max Area: {params['max_area']}",
        f"Epsilon: {params['epsilon_factor']:.3f} | Aspect Ratio: {params['min_aspect_ratio']:.2f}-{params['max_aspect_ratio']:.2f}",
        f"Processing: {stats['total_contours']} -> {stats['area_filtered']} -> {stats['quads_found']} -> {stats['valid_rectangles']}",
        "Real-time Parameter Effects Visualization"
    ]

    colors = [(255, 255, 0), (0, 255, 255), (0, 255, 0), (255, 255, 255)]

    for i, text in enumerate(param_texts):
        cv2.putText(info_panel, text, (10, y_pos),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, colors[i], 1)
        y_pos += 20

    final_display = np.vstack([info_panel, debug_combined])
    return final_display

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    gc.disable()
    print("Jetson Nano版jiguangcar程序启动...")
    
    # 初始化摄像头 - 使用标准OpenCV
    cap = cv2.VideoCapture(0)  # 使用默认摄像头
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    if not cap.isOpened():
        print("无法打开摄像头")
        exit()
    
    # 初始化其他组件
    laser_detector = PurpleLaserDetector()
    buttons = VirtualButtons()
    current_mode = "center"  # 默认选择center模式
    show_video = False  # 视频显示开关，默认开启
    video_window_created = False  # 跟踪视频窗口是否已创建
    show_debug = True  # 调试窗口显示开关，默认开启

    # 初始化可视化调参系统
    param_tuner = ParameterTuner()
    param_tuner.show_tuner = True  # 默认开启调参窗口
    param_tuner.create_trackbars()  # 立即创建调参界面

    # 等待一下让窗口完全创建
    cv2.waitKey(100)

    # 初始化串口通信
    serial_comm = SerialComm(port='/dev/ttyTHS0', baudrate=115200, enable=True)
    serial_comm.init()
    
    # 核心参数
    min_contour_area = 500
    max_contour_area = 120000
    target_sides = 4
    binary_threshold = 66
    # 宽高比限制（过滤过于细长的矩形）
    MIN_ASPECT_RATIO = 0.6   # 宽/高 ≥ 0.6（高不超过宽的1.67倍）
    MAX_ASPECT_RATIO = 1.7   # 宽/高 ≤ 1.7（宽不超过高的1.7倍）
    
    # 透视变换与圆形参数
    corrected_width = 200
    corrected_height = 150
    circle_radius = 50
    circle_num_points = 12
    
    # FPS计算
    fps = 0
    last_time = time.time()
    frame_count = 0
    fps_report_time = time.time()  # FPS报告时间
    fps_report_interval = 2.0      # 每2秒输出一次FPS

    print("🎯 可视化调参系统已启动!")
    print("=" * 60)
    print("📊 已自动打开的窗口:")
    print("  ✅ 主视频窗口 - 显示检测结果")
    print("  ✅ 调参控制窗口 - 实时参数调节")
    print("  ✅ 增强调试窗口 - 处理过程可视化")
    print("")
    print("🎛️ 调参说明:")
    print("  拖动滑动条即可实时调节参数")
    print("  观察调试窗口中的处理效果变化")
    print("  参数调节范围已优化，可安全调节")
    print("")
    print("⌨️ 键盘控制:")
    print("  V - 开启/关闭视频显示")
    print("  D - 开启/关闭调试窗口")
    print("  T - 开启/关闭调参窗口")
    print("  1/2 - 切换中心点/圆形模式")
    print("  Q - 退出程序")
    print("")
    print("💡 提示: 所有窗口已自动打开，开始调参吧!")
    print("=" * 60)
    
    while True:
        frame_count += 1
        
        # 读取摄像头图像
        ret, img_cv = cap.read()
        if not ret:
            print("无法读取摄像头数据")
            break
            
        output = img_cv.copy()
        
        # 处理键盘输入 (确保在显示图像后检查)
        # 先显示图像，再检查键盘输入
        key = cv2.waitKey(1) & 0xFF
        action = None

        if key != 255:  # 有按键按下
            print(f"检测到按键: {key} (字符: {chr(key) if 32 <= key <= 126 else 'N/A'})")
            if key in buttons.key_mapping:
                action = buttons.key_mapping[key]

        if action:
            if action == "center":
                current_mode = "center"
                print("切换到中心点模式")
            elif action == "circle":
                current_mode = "circle"
                print("切换到圆形模式")
            elif action == "thresh_up":
                binary_threshold = min(255, binary_threshold + 3)
                print(f"阈值增加到: {binary_threshold}")
            elif action == "thresh_down":
                binary_threshold = max(1, binary_threshold - 3)
                print(f"阈值减少到: {binary_threshold}")
            elif action == "toggle_video":
                show_video = not show_video
                status = "开启" if show_video else "关闭"
                print(f"视频显示已{status}")
            elif action == "toggle_debug":
                show_debug = not show_debug
                status = "开启" if show_debug else "关闭"
                print(f"调试窗口已{status}")
            elif action == "toggle_trackbar":
                param_tuner.toggle()
                status = "开启" if param_tuner.show_tuner else "关闭"
                print(f"可视化调参窗口已{status}")
            elif action == "quit":
                break
        
        # 计算FPS
        current_time = time.time()
        if current_time - last_time > 0:
            fps = 1.0 / (current_time - last_time)
        last_time = current_time

        # 定期输出FPS到控制台
        if current_time - fps_report_time >= fps_report_interval:
            print(f"当前帧率: {fps:.1f} FPS")
            fps_report_time = current_time
        
        # 获取当前参数（从调参系统或默认值）
        if param_tuner.show_tuner:
            params = param_tuner.get_parameters()
            binary_threshold = params['binary_threshold']
            min_contour_area = params['min_area']
            max_contour_area = params['max_area']
            epsilon_factor = params['epsilon_factor']
            MIN_ASPECT_RATIO = params['min_aspect_ratio']
            MAX_ASPECT_RATIO = params['max_aspect_ratio']

        # 1. 矩形检测（强化筛选逻辑）
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # 统计信息收集
        stats = {
            'total_contours': len(contours),
            'area_filtered': 0,
            'quads_found': 0,
            'valid_rectangles': 0,
            'white_pixels': cv2.countNonZero(binary)
        }

        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            # 第一步：面积过滤
            if not (min_contour_area < area < max_contour_area):
                continue
            stats['area_filtered'] += 1

            # 第二步：多边形逼近（获取轮廓顶点）
            epsilon = epsilon_factor * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            # 检查是否为四边形
            if len(approx) != target_sides:
                continue
            stats['quads_found'] += 1

            # 第三步：宽高比过滤
            x, y, w, h = cv2.boundingRect(approx)
            if h == 0:
                continue
            aspect_ratio = w / h
            if not (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO):
                if show_debug:
                    print(f"过滤宽高比异常: {aspect_ratio:.2f} (w={w}, h={h})")
                continue

            # 第四步：多维度规则性校验（核心过滤逻辑）
            is_regular, reason = is_regular_rectangle(approx)
            if not is_regular:
                if show_debug:
                    print(f"过滤畸形矩形: {reason}")
                continue

            # 所有条件通过，保留该矩形
            quads.append((approx, area))
            stats['valid_rectangles'] += 1

        # 只保留面积最大的矩形
        inner_quads = []
        if quads:
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]
            print(f"检测到 {len(quads)} 个候选矩形，选择最大面积: {largest_quad[1]:.0f}")
        else:
            print("未检测到符合条件的矩形")

        # 显示增强的调试可视化窗口
        if show_debug:
            current_params = {
                'binary_threshold': binary_threshold,
                'min_area': min_contour_area,
                'max_area': max_contour_area,
                'epsilon_factor': epsilon_factor if 'epsilon_factor' in locals() else 0.03,
                'min_aspect_ratio': MIN_ASPECT_RATIO,
                'max_aspect_ratio': MAX_ASPECT_RATIO
            }
            debug_view = create_enhanced_debug_view(img_cv, gray, binary, contours, quads, current_params, stats)
            cv2.imshow('Enhanced Debug View', debug_view)
            # 设置调试窗口位置（左下角）
            cv2.moveWindow('Enhanced Debug View', 50, 400)
        else:
            cv2.destroyWindow('Enhanced Debug View')

        # 更新调参系统显示
        param_tuner.update_display()

        # 2. 根据模式处理数据
        center_points = []
        all_circle_points = []

        for approx, area in inner_quads:
            # 提取顶点
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 绘制内框轮廓
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

            # 使用透视变换校正获得准确中心点
            M, M_inv, src_pts = perspective_transform(pts, corrected_width, corrected_height)
            if M_inv is not None:
                # 校正后矩形的真正几何中心
                corrected_center = (corrected_width//2, corrected_height//2)
                # 将校正后的中心点映射回原图
                center_np = np.array([[corrected_center]], dtype=np.float32)
                original_center = cv2.perspectiveTransform(center_np, M_inv)[0][0]
                cx, cy = int(original_center[0]), int(original_center[1])

                # 绘制校正后的中心点
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))
            else:
                # 备用方案：使用简单几何中心
                cx = int(np.mean(pts[:, 0]))
                cy = int(np.mean(pts[:, 1]))
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))

            # 圆形模式处理
            if current_mode == "circle":
                if M_inv is not None:
                    # 使用透视变换校正的圆轨迹
                    corrected_center = (corrected_width//2, corrected_height//2)
                    corrected_circle = generate_circle_points(
                        corrected_center, circle_radius, circle_num_points
                    )

                    # 将校正后的圆轨迹点映射回原图
                    corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                    original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                    original_points = [(int(x), int(y)) for x, y in original_points]
                    all_circle_points.extend(original_points)

                    # 绘制映射回原图的轨迹点（红色）
                    for (x, y) in original_points:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
                else:
                    # 备用方案：在原图上直接生成圆轨迹
                    simple_circle = generate_circle_points((cx, cy), 30, circle_num_points)
                    all_circle_points.extend(simple_circle)

                    # 绘制简单圆轨迹点（红色）
                    for (x, y) in simple_circle:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)

        # 3. 激光检测 (暂时注释)
        output, laser_points = laser_detector.detect(output)

        # 4. 数据输出和串口发送
        if current_mode == "center":
            # 中心点模式：通过串口发送中心点坐标
            if center_points:
                cx, cy = center_points[0]
                # 通过串口发送中心点坐标（使用0x02指令码）
                serial_comm.send_center_point((cx, cy))
        elif current_mode == "circle":
            # 圆形模式：输出圆的一圈坐标
            if all_circle_points:
                circle_data = f"C,{len(all_circle_points)}"
                for (x, y) in all_circle_points:
                    circle_data += f",{x},{y}"
                print(circle_data)

        # 5. 绘制目标点标记 - 紫色小十字标出(320,240)
        target_x, target_y = 320, 240
        cross_size = 5
        cv2.line(output, (target_x - cross_size, target_y), (target_x + cross_size, target_y), (255, 0, 255), 2)
        cv2.line(output, (target_x, target_y - cross_size), (target_x, target_y + cross_size), (255, 0, 255), 2)
        cv2.putText(output, f"({target_x},{target_y})", (target_x + 8, target_y - 8),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

        # 6. 绘制虚拟按键
        buttons.draw_buttons(output, current_mode, binary_threshold)

        # 7. 显示统计信息
        cv2.putText(output, f"FPS: {fps:.1f}", (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 显示当前模式
        mode_text = f"Mode: {current_mode.upper()}"
        cv2.putText(output, mode_text, (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        # 显示视频状态
        video_status = f"Video: {'ON' if show_video else 'OFF'}"
        video_color = (0, 255, 0) if show_video else (0, 0, 255)  # 绿色开启，红色关闭
        cv2.putText(output, video_status, (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, video_color, 1)

        # 显示调试状态
        debug_status = f"Debug: {'ON' if show_debug else 'OFF'}"
        debug_color = (0, 255, 0) if show_debug else (0, 0, 255)
        cv2.putText(output, debug_status, (10, 80),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, debug_color, 1)

        # 显示调参系统状态
        tuner_status = f"Tuner: {'ON' if param_tuner.show_tuner else 'OFF'}"
        tuner_color = (0, 255, 0) if param_tuner.show_tuner else (0, 0, 255)
        cv2.putText(output, tuner_status, (10, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, tuner_color, 1)

        # 显示串口状态
        serial_status = f"Serial: {'ON' if serial_comm.connection else 'OFF'}"
        serial_color = (0, 255, 0) if serial_comm.connection else (0, 0, 255)  # 绿色连接，红色断开
        cv2.putText(output, serial_status, (10, 120),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, serial_color, 1)

        # 显示当前参数
        param_text = f"T:{binary_threshold} Area:{min_contour_area}-{max_contour_area}"
        cv2.putText(output, param_text, (10, 140),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)

        # 显示检测统计
        if 'stats' in locals():
            stats_text = f"Detect: {stats['total_contours']}->{stats['area_filtered']}->{stats['quads_found']}->{stats['valid_rectangles']}"
            cv2.putText(output, stats_text, (10, 160),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        # 显示图像 (根据开关控制)
        if show_video:
            cv2.imshow('Jetson Nano Vision System', output)
            if not video_window_created:
                # 设置主窗口位置（左上角）
                cv2.moveWindow('Jetson Nano Vision System', 50, 50)
                video_window_created = True
                print("✅ 主视频窗口已创建")
            # 确保窗口获得焦点
            cv2.setWindowProperty('Jetson Nano Vision System', cv2.WND_PROP_TOPMOST, 1)
            cv2.setWindowProperty('Jetson Nano Vision System', cv2.WND_PROP_TOPMOST, 0)
        else:
            # 如果关闭视频显示且窗口已创建，销毁窗口
            if video_window_created:
                cv2.destroyWindow('Jetson Nano Vision System')
                video_window_created = False

    # 清理资源
    cap.release()
    cv2.destroyAllWindows()
    serial_comm.close()  # 关闭串口连接
    print("程序已退出")

