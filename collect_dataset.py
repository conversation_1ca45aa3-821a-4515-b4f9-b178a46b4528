import cv2
import os
import argparse
import time

# 参数解析器
parser = argparse.ArgumentParser(description='数据集收集脚本')
parser.add_argument('--output', type=str, default='./dataset', help='图像保存路径')
parser.add_argument('--prefix', type=str, default='img', help='图像文件名前缀')
parser.add_argument('--format', type=str, default='jpg', choices=['jpg', 'png'], help='图像保存格式')
parser.add_argument('--width', type=int, default=640, help='摄像头分辨率宽度')
parser.add_argument('--height', type=int, default=480, help='摄像头分辨率高度')
parser.add_argument('--delay', type=float, default=0.5, help='捕获间隔时间(秒)')
args = parser.parse_args()

# 创建输出目录
os.makedirs(args.output, exist_ok=True)
print(f"数据集将保存到: {os.path.abspath(args.output)}")

# 初始化摄像头
cap = cv2.VideoCapture(0)  # 0表示第一个USB摄像头

# 设置摄像头分辨率
cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.width)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.height)

# 检查摄像头是否成功打开
if not cap.isOpened():
    print("错误：无法打开摄像头")
    exit()

# 获取已存在的文件数量（用于连续编号）
existing_files = [f for f in os.listdir(args.output) if f.startswith(args.prefix)]
count = len(existing_files)

print("数据集收集说明:")
print("1. 按 'C' 键或空格键捕获当前帧")
print("2. 按 'Q' 键退出程序")
print("3. 捕获间隔已设为: {:.1f}秒".format(args.delay))

last_capture_time = 0

try:
    while True:
        # 读取帧
        ret, frame = cap.read()
        if not ret:
            print("错误：无法获取帧")
            break

        # 显示实时画面
        cv2.imshow('Dataset Collection', frame)
        
        # 按键检测 (非阻塞等待)
        key = cv2.waitKey(1) & 0xFF
        
        current_time = time.time()
        
        # 捕获图像逻辑
        if (key == ord('c') or key == ord(' ')) and (current_time - last_capture_time > args.delay):
            # 生成唯一文件名
            filename = f"{args.prefix}_{count:05d}.{args.format}"
            save_path = os.path.join(args.output, filename)
            
            # 保存图像（使用95%质量的JPEG或无损PNG）
            if args.format == 'jpg':
                cv2.imwrite(save_path, frame, [int(cv2.IMWRITE_JPEG_QUALITY), 95])
            else:
                cv2.imwrite(save_path, frame)
            
            print(f"已保存: {filename}")
            count += 1
            last_capture_time = current_time
        
        # 退出程序
        if key == ord('q'):
            break

finally:
    # 释放资源
    cap.release()
    cv2.destroyAllWindows()
    print(f"\n数据集收集完成。共收集 {count} 张图像。")
    print(f"请将 '{os.path.abspath(args.output)}' 文件夹复制到训练用台式机")