import cv2
import numpy as np

# 读取图像，以灰度模式读取
image = cv2.imread('yolo11/3.png', cv2.IMREAD_GRAYSCALE)
# 阈值处理，二值化，将图像转为黑白，便于轮廓检测，这里根据实际情况调整阈值
_, binary = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY_INV)

# 查找轮廓
contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

# 遍历轮廓，筛选出矩形轮廓（这里简单通过近似多边形后的边数判断，也可结合面积等条件）
rect_contour = None
for contour in contours:
    approx = cv2.approxPolyDP(contour, 0.01 * cv2.arcLength(contour, True), True)
    if len(approx) == 4:
        rect_contour = approx
        break

if rect_contour is not None:
    # 在原彩色图像上绘制矩形轮廓（方便可视化，若输入是灰度图，可先转彩色）
    color_image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
    cv2.drawContours(color_image, [rect_contour], -1, (0, 255, 0), 2)

    # 提取矩形区域的感兴趣区域（ROI）
    x, y, w, h = cv2.boundingRect(rect_contour)
    roi = binary[y:y + h, x:x + w]

    # 再次查找 ROI 内的轮廓，获取内部线条轮廓
    inner_contours, _ = cv2.findContours(roi, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 对内部轮廓进行角点检测（以 Shi - Tomasi 角点检测为例）
    for inner_contour in inner_contours:
        # 将轮廓转为点集
        points = np.float32(inner_contour.reshape(-1, 2))
        # Shi - Tomasi 角点检测参数设置
        max_corners = 10  # 最多检测角点数量
        quality_level = 0.01  # 角点质量水平
        min_distance = 10  # 角点间最小距离
        corners = cv2.goodFeaturesToTrack(points, max_corners, quality_level, min_distance)

        if corners is not None:
            corners = np.int0(corners)
            # 遍历角点，尝试连线（这里简单按顺序连线，实际可根据需求调整逻辑）
            for i in range(len(corners) - 1):
                pt1 = (corners[i][0][0] + x, corners[i][0][1] + y)
                pt2 = (corners[i + 1][0][0] + x, corners[i + 1][0][1] + y)
                # 绘制线段，确保在黑线范围内（这里通过判断线段是否在轮廓内等方式简单处理，可更精细）
                cv2.line(color_image, pt1, pt2, (0, 0, 255), 2)

    # 显示结果图像
    cv2.imshow('Result', color_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
else:
    print("未检测到矩形轮廓")