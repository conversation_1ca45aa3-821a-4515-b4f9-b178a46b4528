# version:1.5.0
import cv2
import numpy as np
import os
import matplotlib
import matplotlib.pyplot as plt
from datetime import datetime
from scipy.interpolate import splprep, splev
import math
from collections import deque

# 设置支持中文的字体 - 解决中文显示问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题


def detect_black_box(image_path):
    """检测图片中的黑框并返回其坐标"""
    # 读取图片
    original_img = cv2.imread(image_path)
    if original_img is None:
        print(f"无法读取图片: {image_path}")
        return None, None

    # 转换为灰度图
    gray = cv2.cvtColor(original_img, cv2.COLOR_BGR2GRAY)

    # 高斯模糊减少噪点
    blurred = cv2.G<PERSON>sianBlur(gray, (5, 5), 0)

    # 使用Canny边缘检测
    edges = cv2.Canny(blurred, 50, 150)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 找到最大的矩形轮廓（黑框）
    max_area = 0
    bounding_rect = None

    for contour in contours:
        # 近似多边形，减少轮廓点数
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # 计算面积
        area = cv2.contourArea(approx)
        if area > max_area:
            max_area = area
            bounding_rect = cv2.boundingRect(approx)

    if bounding_rect is None:
        print("未检测到黑框")
        return None, original_img

    x, y, w, h = bounding_rect

    # 扩展边界以确保包含整个黑框
    padding = -10
    x = max(0, x - padding)
    y = max(0, y - padding)
    w = min(original_img.shape[1] - x, w + 2 * padding)
    h = min(original_img.shape[0] - y, h + 2 * padding)

    return (x, y, w, h), original_img


def extract_center_trajectory(image, box):
    """提取曲线中心轨迹并返回有序轨迹点"""
    if box is None:
        return None, None, None

    x, y, w, h = box
    box_region = image[y:y + h, x:x + w].copy()
    gray = cv2.cvtColor(box_region, cv2.COLOR_BGR2GRAY)
    filtered = cv2.medianBlur(gray, 15)
    thresh = cv2.adaptiveThreshold(
        filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV, 31, 7
    )
    kernel = np.ones((3, 3), np.uint8)
    opened = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=1)
    closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel, iterations=2)
    skeleton = cv2.ximgproc.thinning(closed, thinningType=cv2.ximgproc.THINNING_GUOHALL)

    # 从骨架中提取所有点坐标
    points = np.column_stack(np.where(skeleton > 0))

    # 将坐标转换为(x, y)格式
    points = points[:, [1, 0]]  # 交换列顺序

    # 如果点集为空则返回
    if len(points) == 0:
        return None, None, None

    # 使用拓扑排序获取有序轨迹点
    sorted_points = topological_sort_points(points)

    # 创建可视化图像
    result = np.ones_like(box_region) * 255
    result[skeleton > 0] = 0

    return sorted_points, (x, y), result


def topological_sort_points(points):
    """使用拓扑排序获取有序轨迹点"""
    # 将点转换为元组集合以便快速查找
    point_set = set(tuple(p) for p in points)

    # 如果没有点，返回空列表
    if not point_set:
        return []

    # 找到端点（只有一个邻居的点）
    endpoints = []
    for p in point_set:
        neighbors = get_8_neighbors(p, point_set)
        if len(neighbors) == 1:
            endpoints.append(p)

    # 如果没有端点（闭环），则使用x坐标最小的点
    if not endpoints:
        start_point = min(point_set, key=lambda p: p[0])
    else:
        # 使用x坐标最小的端点作为起点
        start_point = min(endpoints, key=lambda p: p[0])

    # 使用BFS遍历点
    visited = set()
    queue = deque([start_point])
    sorted_points = []

    while queue:
        current = queue.popleft()
        if current in visited:
            continue

        visited.add(current)
        sorted_points.append(current)

        # 获取当前点的邻居
        neighbors = get_8_neighbors(current, point_set)

        # 按方向连续性排序邻居（优先选择与上一方向一致的邻居）
        if len(sorted_points) > 1:
            prev_point = sorted_points[-2]
            dx = current[0] - prev_point[0]
            dy = current[1] - prev_point[1]

            # 优先选择方向一致的邻居
            neighbors.sort(key=lambda n: abs((n[0] - current[0]) - dx) + abs((n[1] - current[1]) - dy))

        # 添加未访问的邻居
        for neighbor in neighbors:
            if neighbor not in visited:
                queue.append(neighbor)

    # 转换为numpy数组
    return np.array([list(p) for p in sorted_points])


def get_8_neighbors(point, point_set):
    """获取8邻域内的邻居点"""
    x, y = point
    neighbors = []

    # 检查8个方向
    for dx in [-1, 0, 1]:
        for dy in [-1, 0, 1]:
            if dx == 0 and dy == 0:
                continue

            neighbor = (x + dx, y + dy)
            if neighbor in point_set:
                neighbors.append(neighbor)

    return neighbors


def interpolate_trajectory(points, num_points=100):
    """对轨迹点进行样条插值"""
    if points is None or len(points) < 4:
        return points

    # 添加0.1%的随机抖动避免重复x值
    jitter = np.random.normal(0, 0.001, points.shape)
    points = points.astype(float) + jitter

    # 样条插值
    tck, u = splprep(points.T, u=None, s=0.0, per=0)
    u_new = np.linspace(u.min(), u.max(), num_points)
    x_new, y_new = splev(u_new, tck, der=0)

    return np.column_stack((x_new, y_new))


def main():
    image_path = 'yolo11/3.png'
    box, original_img = detect_black_box(image_path)

    if box is None:
        print("未检测到黑框，无法继续处理")
        return

    # 提取轨迹点和偏移量
    trajectory_points, box_offset, center_image = extract_center_trajectory(original_img, box)

    if trajectory_points is None or len(trajectory_points) == 0:
        print("无法提取轨迹点")
        return

    # 转换到原图坐标系
    if box_offset is not None:
        trajectory_points[:, 0] += box_offset[0]
        trajectory_points[:, 1] += box_offset[1]

    # 插值平滑轨迹
    interpolated_points = interpolate_trajectory(trajectory_points, 200)

    # 保存结果
    save_dir = "curve_results"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存轨迹点数据
    trajectory_path = os.path.join(save_dir, f"trajectory_points_{timestamp}.npy")
    np.save(trajectory_path, interpolated_points)
    print(f"轨迹点数据已保存至: {trajectory_path}")

    # 可视化轨迹点
    plt.figure(figsize=(12, 8))

    # 将OpenCV的BGR图像转换为RGB格式显示
    img_rgb = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)
    plt.imshow(img_rgb)

    # 绘制原始轨迹点（按顺序）
    for i in range(1, len(trajectory_points)):
        plt.plot([trajectory_points[i - 1, 0], trajectory_points[i, 0]],
                 [trajectory_points[i - 1, 1], trajectory_points[i, 1]],
                 'r-', linewidth=1, alpha=0.5)

    # 绘制插值后轨迹
    plt.plot(interpolated_points[:, 0], interpolated_points[:, 1],
             'b-', linewidth=2, label='插值轨迹')

    plt.legend()
    plt.title('曲线轨迹点')

    # 保存可视化结果
    plot_path = os.path.join(save_dir, f"trajectory_plot_{timestamp}.png")
    plt.savefig(plot_path)
    print(f"轨迹可视化图已保存至: {plot_path}")

    # 显示结果
    plt.show()

    # 显示中心轨迹图像
    if center_image is not None:
        cv2.imshow("Center Trajectory", center_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()


if __name__ == "__main__":
    main()