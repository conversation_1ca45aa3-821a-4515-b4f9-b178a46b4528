import cv2
import numpy as np
import time

def order_points_clockwise(pts):
    """将轮廓点按顺时针方向排序（不使用多边形近似）"""
    # 计算轮廓的中心点
    center = np.mean(pts, axis=0)
    
    # 计算每个点相对于中心的角度
    angles = np.arctan2(pts[:, 1] - center[1], pts[:, 0] - center[0])
    
    # 将角度转换为0到2π范围
    angles = np.mod(angles, 2 * np.pi)
    
    # 按角度排序（顺时针）
    sorted_indices = np.argsort(angles)
    
    return pts[sorted_indices]

# 初始化摄像头
cap = cv2.VideoCapture(0)

# 设置窗口
cv2.namedWindow('Edge Detection', cv2.WINDOW_NORMAL)
cv2.namedWindow('Contour Borders', cv2.WINDOW_NORMAL)
cv2.namedWindow('Processed', cv2.WINDOW_NORMAL)

# 性能计数器
fps = 0
frame_count = 0
start_time = time.time()

while True:
    # 读取摄像头帧
    ret, frame = cap.read()
    if not ret:
        break
    
    # 转换为灰度图
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # 高斯模糊降噪
    blurred = cv2.GaussianBlur(gray, (7, 7), 0)
    
    # 自动计算Canny阈值
    sigma = 0.33
    v = np.median(blurred)
    lower = int(max(0, (1.0 - sigma) * v))
    upper = int(min(255, (1.0 + sigma) * v))
    
    # Canny边缘检测
    edges = cv2.Canny(blurred, lower, upper)
    
    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    
    # 创建绘制轮廓的图像
    contour_img = np.zeros_like(frame)
    processed_img = frame.copy()
    
    # 过滤小轮廓并处理
    min_contour_area = 300
    for contour in contours:
        # 跳过小轮廓
        if cv2.contourArea(contour) < min_contour_area:
            continue
            
        # 展平轮廓点
        pts = contour.reshape(-1, 2)
        
        # 按顺时针排序
        ordered_pts = order_points_clockwise(pts)
        
        # 在轮廓图像上绘制顺时针边框
        for i in range(len(ordered_pts)):
            start_point = tuple(ordered_pts[i].astype(int))
            end_point = tuple(ordered_pts[(i + 1) % len(ordered_pts)].astype(int))
            cv2.line(contour_img, start_point, end_point, (0, 255, 0), 2)
        
        # 在原始图像上绘制轮廓点
        for pt in ordered_pts:
            cv2.circle(processed_img, tuple(pt.astype(int)), 2, (0, 0, 255), -1)
        
        # 绘制中心点
        center = np.mean(ordered_pts, axis=0).astype(int)
        cv2.circle(processed_img, tuple(center), 5, (255, 0, 0), -1)
    
    # 计算FPS
    frame_count += 1
    if frame_count >= 10:
        end_time = time.time()
        fps = frame_count / (end_time - start_time)
        frame_count = 0
        start_time = time.time()
    
    # 在图像上显示FPS
    cv2.putText(processed_img, f"FPS: {fps:.1f}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
    cv2.putText(contour_img, f"Contour Points: {sum(len(c) for c in contours)}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    
    # 显示结果
    cv2.imshow('Edge Detection', edges)
    cv2.imshow('Contour Borders', contour_img)
    cv2.imshow('Processed', processed_img)
    
    # 按ESC退出
    if cv2.waitKey(1) & 0xFF == 27:
        break

# 释放资源
cap.release()
cv2.destroyAllWindows()