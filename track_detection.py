import cv2
import numpy as np
import math

class BlackBlockTracker:
    def __init__(self):
        # 黑色阈值参数 (HSV色彩空间更稳定)
        self.black_lower = np.array([0, 0, 0])
        self.black_upper = np.array([180, 255, 60])
        
        # 形态学操作核
        self.kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        
    def detect_black_blocks(self, img):
        """检测黑色块并返回轮廓"""
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 创建黑色掩码
        mask = cv2.inRange(hsv, self.black_lower, self.black_upper)
        
        # 形态学操作去噪
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, self.kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, self.kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 过滤面积太小的轮廓
        valid_contours = [c for c in contours if cv2.contourArea(c) > 500]
        
        return valid_contours, mask
    
    def extract_center_points(self, contours):
        """提取轮廓中心点"""
        centers = []
        for contour in contours:
            # 计算轮廓的矩
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                centers.append((cx, cy))
        return centers
    
    def sort_points_clockwise(self, points):
        """按顺时针方向排序点"""
        if len(points) < 3:
            return points
            
        # 计算几何中心
        center_x = sum(p[0] for p in points) / len(points)
        center_y = sum(p[1] for p in points) / len(points)
        
        def angle_from_center(point):
            dx = point[0] - center_x
            dy = point[1] - center_y
            return math.atan2(dy, dx)
        
        # 按角度排序（顺时针）
        sorted_points = sorted(points, key=angle_from_center, reverse=True)
        
        # 添加第一个点使路径闭合
        if len(sorted_points) > 0:
            sorted_points.append(sorted_points[0])
            
        return sorted_points, (int(center_x), int(center_y))
    
    def generate_smooth_path(self, points, segments=20):
        """在关键点之间生成平滑路径"""
        if len(points) < 2:
            return points
            
        smooth_path = []
        for i in range(len(points) - 1):
            start = points[i]
            end = points[i + 1]
            
            # 在两点间插值
            for j in range(segments):
                ratio = j / segments
                px = int(start[0] + (end[0] - start[0]) * ratio)
                py = int(start[1] + (end[1] - start[1]) * ratio)
                smooth_path.append((px, py))
                
        return smooth_path
    
    def process_frame(self, img):
        """完整的处理流程"""
        # 1. 检测黑色块
        contours, mask = self.detect_black_blocks(img)
        
        # 2. 提取中心点
        centers = self.extract_center_points(contours)
        
        if len(centers) < 3:
            return None, None, mask
        
        # 3. 几何排序
        sorted_points, geometric_center = self.sort_points_clockwise(centers)
        
        # 4. 生成平滑路径
        smooth_path = self.generate_smooth_path(sorted_points)
        
        return sorted_points, smooth_path, mask
    
    def visualize_results(self, img, sorted_points, smooth_path, mask):
        """可视化检测结果"""
        result_img = img.copy()
        
        if sorted_points:
            # 绘制关键点
            for i, point in enumerate(sorted_points[:-1]):  # 排除重复的闭合点
                cv2.circle(result_img, point, 8, (0, 255, 0), -1)
                cv2.putText(result_img, str(i), (point[0]+10, point[1]), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 绘制路径连线
            for i in range(len(sorted_points) - 1):
                cv2.line(result_img, sorted_points[i], sorted_points[i+1], (255, 0, 0), 2)
        
        if smooth_path:
            # 绘制平滑路径点
            for point in smooth_path[::5]:  # 每5个点绘制一个
                cv2.circle(result_img, point, 2, (0, 0, 255), -1)
        
        return result_img

# 使用示例
def main():
    tracker = BlackBlockTracker()
    cap = cv2.VideoCapture(0)  # 或者使用图片路径
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        # 处理帧
        sorted_points, smooth_path, mask = tracker.process_frame(frame)
        
        # 可视化
        result = tracker.visualize_results(frame, sorted_points, smooth_path, mask)
        
        # 显示结果
        cv2.imshow('Original', frame)
        cv2.imshow('Mask', mask)
        cv2.imshow('Result', result)
        
        # 打印坐标信息
        if sorted_points:
            print(f"关键点坐标: {sorted_points[:-1]}")  # 排除重复点
            print(f"路径点数量: {len(smooth_path) if smooth_path else 0}")
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()