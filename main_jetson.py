import cv2
import numpy as np
import time
import gc
from serial_comm import SerialComm

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = MORPH_KERNEL

    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        mask_purple = cv2.inRange(hsv, LOWER_PURPLE, UPPER_PURPLE)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_OPEN, self.kernel)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        best_laser_point = None
        best_score = -1

        for cnt in contours_purple:
            area = cv2.contourArea(cnt)
            if not (LASER_MIN_AREA <= area <= LASER_MAX_AREA):
                continue

            perimeter = cv2.arcLength(cnt, True)
            if perimeter == 0: continue
            circularity = 4 * np.pi * area / (perimeter * perimeter)
            if circularity < LASER_MIN_CIRCULARITY:
                continue

            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            v_value_at_center = hsv[cy, cx, 2]
            if v_value_at_center < LASER_MIN_BRIGHTNESS_V:
                continue

            score = v_value_at_center
            if score > best_score:
                best_score = score
                best_laser_point = (cx, cy)

        if best_laser_point is not None:
            cx, cy = best_laser_point
            cv2.circle(img, (cx, cy), self.pixel_radius, (255, 0, 255), -1)
            cv2.putText(img, "Purple", (cx - 20, cy - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
            return img, [best_laser_point]

        return img, []

# --------------------------- 配置参数 ---------------------------
# 摄像头参数
CAM_WIDTH, CAM_HEIGHT = 640, 480  # Jetson 推荐分辨率
DISPLAY_FPS = 30

# 紫色激光检测参数
PIXEL_RADIUS = 3
LOWER_PURPLE = np.array([54, 30, 56])
UPPER_PURPLE = np.array([230, 178, 255])
MORPH_KERNEL = np.ones((3, 3), np.uint8)
LASER_MIN_AREA = 5
LASER_MAX_AREA = 200
LASER_MIN_CIRCULARITY = 0.3
LASER_MIN_BRIGHTNESS_V = 150

# A4纸检测参数
LOWER_WHITE_HSV = np.array([0, 0, 0])
UPPER_WHITE_HSV = np.array([250, 20, 250])
A4_MIN_AREA = 50
A4_MAX_AREA = 100000
A4_ASPECT_RATIO = 1.414
ASPECT_TOLERANCE = 0.2

# 模式切换参数
MODE_A4 = 0
MODE_LASER = 1
current_mode = MODE_A4
SWITCH_INTERVAL = 30  # 每30帧切换一次模式

# 形状检测参数
DO_DILATION = True
DILATION_KERNEL_SIZE = 3
DILATION_ITERATIONS = 1
TRIANGLE_SCALE = 0.6
TRIANGLE_POS_X = 0.5
TRIANGLE_POS_Y = 0.5
POINTS_PER_EDGE = 3

# 显示控制
SHOW_WINDOWS = True

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    print("Jetson A4纸检测与紫色激光检测程序启动...")
    
    # 初始化摄像头 (CSI摄像头)
    gst_pipeline = f"nvarguscamerasrc ! video/x-raw(memory:NVMM), width={CAM_WIDTH}, height={CAM_HEIGHT}, format=NV12, framerate={DISPLAY_FPS}/1 ! nvvidconv ! video/x-raw, format=BGRx ! videoconvert ! video/x-raw, format=BGR ! appsink"
    cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)
    
    # 如果CSI摄像头失败，尝试USB摄像头
    if not cap.isOpened():
        print("CSI摄像头初始化失败，尝试USB摄像头...")
        cap = cv2.VideoCapture(0)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, CAM_WIDTH)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, CAM_HEIGHT)
        cap.set(cv2.CAP_PROP_FPS, DISPLAY_FPS)
    
    if not cap.isOpened():
        print("摄像头初始化失败")
        exit()
    
    print(f"摄像头初始化成功: {CAM_WIDTH}x{CAM_HEIGHT}@{DISPLAY_FPS}fps")
    
    # 初始化检测器
    detector = PurpleLaserDetector(pixel_radius=PIXEL_RADIUS)
    
    # 初始化串口
    serial_comm = SerialComm('/dev/ttyTHS0', 115200, True)
    serial_comm.init()
    
    # 性能监控
    frame_count = 0
    fps_start_time = time.time()
    generated_points = []
    
    try:
        while True:
            frame_count += 1
            
            # 每SWITCH_INTERVAL帧切换一次模式
            if frame_count % SWITCH_INTERVAL == 0:
                current_mode = MODE_LASER if current_mode == MODE_A4 else MODE_A4
                mode_name = "激光检测" if current_mode == MODE_LASER else "A4纸检测"
                print(f"切换到{mode_name}模式")
            
            # 读取图像
            ret, img_cv = cap.read()
            if not ret:
                print("无法读取摄像头画面")
                continue
                
            img_display = img_cv.copy()
            
            # 初始化变量
            a4_count = 0
            laser_points = []
            
            # --------------------------- 模式1：A4纸检测与点生成 ---------------------------
            if current_mode == MODE_A4:
                hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
                white_mask = cv2.inRange(hsv, LOWER_WHITE_HSV, UPPER_WHITE_HSV)
                
                if DO_DILATION:
                    kernel = np.ones((DILATION_KERNEL_SIZE, DILATION_KERNEL_SIZE), np.uint8)
                    white_mask = cv2.dilate(white_mask, kernel, iterations=DILATION_ITERATIONS)
                    white_mask = cv2.erode(white_mask, kernel, iterations=DILATION_ITERATIONS)
                
                contours, _ = cv2.findContours(white_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                a4_candidates = []
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if not (A4_MIN_AREA <= area <= A4_MAX_AREA):
                        continue
                    
                    perimeter = cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, 0.03 * perimeter, True)
                    
                    if len(approx) == 4:
                        rect = cv2.minAreaRect(approx)
                        width, height = rect[1]
                        aspect_ratio = max(width, height) / min(width, height)
                        
                        if abs(aspect_ratio - A4_ASPECT_RATIO) <= ASPECT_TOLERANCE:
                            a4_candidates.append(approx)
                
                if a4_candidates:
                    a4_candidates.sort(key=lambda c: cv2.contourArea(c), reverse=True)
                    a4_contour = a4_candidates[0]
                    a4_count = 1
                    
                    pts = a4_contour.reshape(4, 2).astype(np.float32)
                    s = pts.sum(axis=1)
                    tl = pts[np.argmin(s)]
                    br = pts[np.argmax(s)]
                    diff = np.diff(pts, axis=1)
                    tr = pts[np.argmin(diff)]
                    bl = pts[np.argmax(diff)]
                    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
                    
                    cv2.drawContours(img_display, [a4_contour], -1, (0, 255, 0), 2)
                    center = (int((tl[0]+br[0])/2), int((tl[1]+br[1])/2))
                    cv2.circle(img_display, center, 3, (0, 0, 255), -1)
                    cv2.putText(img_display, "A4 Paper", (int(tl[0]), int(tl[1]-10)),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                    
                    width = int(np.linalg.norm(tl - tr))
                    height = int(width / A4_ASPECT_RATIO)
                    dst_pts = np.array([
                        [0, 0], [width - 1, 0],
                        [width - 1, height - 1], [0, height - 1]
                    ], dtype=np.float32)
                    
                    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
                    ret, M_inv = cv2.invert(M)
                    if ret:
                        tri_center_x = int(width * TRIANGLE_POS_X)
                        tri_center_y = int(height * TRIANGLE_POS_Y)
                        tri_size = int(min(width, height) * TRIANGLE_SCALE)
                        tri_h = int(tri_size * 0.866)
                        tri_pts = np.array([
                            [tri_center_x, tri_center_y - tri_h // 2],
                            [tri_center_x - tri_size // 2, tri_center_y + tri_h // 2],
                            [tri_center_x + tri_size // 2, tri_center_y + tri_h // 2]
                        ], dtype=np.float32)
                        
                        generated_points = []
                        for i in range(3):
                            p1, p2 = tri_pts[i], tri_pts[(i+1) % 3]
                            for j in range(POINTS_PER_EDGE + 1):
                                t = j / POINTS_PER_EDGE
                                x = p1[0] + t * (p2[0] - p1[0])
                                y = p1[1] + t * (p2[1] - p1[1])
                                generated_points.append((x, y))
                        
                        if generated_points:
                            points_array = np.array([generated_points], dtype=np.float32)
                            mapped_points = cv2.perspectiveTransform(points_array, M_inv)[0]
                            
                            # 发送点坐标到串口
                            point_count = len(mapped_points)
                            point_str = ",".join([f"{int(x)},{int(y)}" for x, y in mapped_points])
                            print(f"发送A4纸点坐标: M,{point_count},{point_str}")
                            
                            for x, y in mapped_points:
                                x, y = int(x), int(y)
                                cv2.circle(img_display, (x, y), 2, (255, 0, 0), -1)
            
            # --------------------------- 模式2：激光检测 ---------------------------
            else:
                laser_img, laser_points = detector.detect(img_cv.copy())
                img_display = laser_img
                
                if laser_points:
                    x, y = laser_points[0]
                    serial_comm.send_red_laser_point((x, y))
                    print(f"发送紫色激光点: P,{x},{y}")
            
            # --------------------------- 显示统计信息 ---------------------------
            current_time = time.time()
            elapsed_time = current_time - fps_start_time
            fps = int(frame_count / elapsed_time) if elapsed_time > 0 else 0
            
            mode_text = "A4检测" if current_mode == MODE_A4 else "激光检测"
            stats_text = f"FPS:{fps} | 模式:{mode_text} | A4:{a4_count} | 激光:{len(laser_points)}"
            cv2.putText(img_display, stats_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # --------------------------- 显示图像 ---------------------------
            if SHOW_WINDOWS:
                cv2.imshow('Jetson Detection', img_display)
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('m'):  # 手动切换模式
                    current_mode = MODE_LASER if current_mode == MODE_A4 else MODE_A4
                    mode_name = "激光检测" if current_mode == MODE_LASER else "A4纸检测"
                    print(f"手动切换到{mode_name}模式")
            
            # 性能监控
            if frame_count % 100 == 0:
                print(f"性能统计: {fps} FPS, 处理了{frame_count}帧")
                
    except KeyboardInterrupt:
        print("程序被用户中断")
    finally:
        cap.release()
        cv2.destroyAllWindows()
        serial_comm.close()
        print("程序结束，资源已释放")